# 项目文档索引

## 📚 核心文档

### 🚀 项目概览
- **[README.md](README.md)** - 项目主要说明文档，包含系统架构、功能模块、部署指南等

### 💬 WebSocket 和消息系统
- **[WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md](WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md)** - WebSocket消息广播完整对接文档
  - 涵盖54种不同的WebSocket消息类型
  - 按用户角色分类：用户消息(23种)、商家消息(17种)、管理员消息(14种)
  - 包含完整的前端对接代码示例
  - 消息优先级和业务跳转处理指南

### 🔧 技术实现文档
- **[modules/chat/docs/notification-system.md](modules/chat/docs/notification-system.md)** - 通知系统详细技术文档
- **[NOTIFICATION_DATA_IMPLEMENTATION.md](NOTIFICATION_DATA_IMPLEMENTATION.md)** - NotificationData字段实现文档

## 📋 文档使用指南

### 🎯 前端开发者
1. 首先阅读 `WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md` 了解所有WebSocket消息类型
2. 参考文档中的前端对接代码示例实现消息处理逻辑
3. 根据消息优先级设计不同的UI展示效果
4. 使用NotificationData字段实现业务跳转功能

### 🔨 后端开发者
1. 参考 `modules/chat/docs/notification-system.md` 了解通知系统架构
2. 查看 `WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md` 中的消息结构定义
3. 按照文档规范添加新的消息类型和事件

### 🏗️ 系统架构师
1. 阅读 `README.md` 了解整体系统架构
2. 参考WebSocket消息广播文档了解实时通信设计
3. 查看技术特点部分了解系统的扩展性和可靠性设计

## 🔄 文档更新记录

### 2025-01-30
- ✅ 创建 `WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md` - WebSocket消息广播完整对接文档
- ✅ 更新 `README.md` - 添加WebSocket文档索引
- ✅ 创建 `DOCUMENTATION_INDEX.md` - 文档索引页面

### 历史更新
- ✅ 实现NotificationData字段功能
- ✅ 完成WebSocket消息持久化bug修复
- ✅ 重构WebSocket通知系统按用户角色分类

## 📞 技术支持

如果您在使用文档过程中遇到问题，请：
1. 首先查看相关文档的技术特点和FAQ部分
2. 检查代码示例是否正确实现
3. 确认消息结构和字段定义是否符合规范

## 🎯 快速导航

| 需求场景 | 推荐文档 |
|---------|---------|
| 了解项目整体架构 | [README.md](README.md) |
| 实现WebSocket消息处理 | [WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md](WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md) |
| 添加新的通知类型 | [modules/chat/docs/notification-system.md](modules/chat/docs/notification-system.md) |
| 实现前端业务跳转 | [NOTIFICATION_DATA_IMPLEMENTATION.md](NOTIFICATION_DATA_IMPLEMENTATION.md) |
| 部署和配置系统 | [README.md](README.md) - 部署指南部分 |
