# WebSocket消息格式修复总结

## 🔍 问题分析

### 发现的问题
经过测试发现，前端收到的WebSocket消息格式与文档描述不一致：

**前端实际收到的消息格式**：
```json
{
  "type": "message",
  "event": "new_message",
  "session_id": 12,
  "timestamp": 1753845215,
  "data": {
    "id": 137,
    "session_id": 12,
    "sender_id": 1,
    "sender_type": "merchant",
    "sender_name": "songda",
    "sender_avatar": "http://omallimg.qwyx.shop/merchant_logo/2025/06/10/1749519350919156000_DZtRIVc0.png",
    "content": "测试文本消息",
    "type": "text",
    "resource_id": "",
    "status": 0,
    "created_at": "2025-07-30T11:13:35+08:00"
  }
}
```

**文档中描述的格式**：
```json
{
  "type": "message",
  "event": "text_message",
  "data": {
    "sender_id": 1001,
    "sender_type": "user",
    "target_id": 2001,
    "target_type": "merchant",
    "content": "你好，请问有什么可以帮助你的？",
    "message_type": "text"
  }
}
```

### 根本原因
系统中存在两个不同的WebSocket消息发送路径：

1. **聊天消息路径**（实际使用的）：
   - 位置：`modules/chat/controllers/message_controller.go`
   - 事件名称：`"new_message"`
   - 数据结构：完整的MessageDTO对象，包含发送者信息

2. **通用WebSocket服务路径**（文档描述的）：
   - 位置：`modules/chat/services/websocket_common.go`
   - 事件名称：`"text_message"`
   - 数据结构：简化的map结构

## 🛠️ 解决方案

### 1. 统一事件名称
修改了 `message_controller.go` 中的 `pushMessageToWebSocket` 方法：

```go
// 根据消息类型确定事件名称
var eventName string
switch chatMessage.Type {
case "text":
    eventName = "text_message"
case "image":
    eventName = "media_message"
case "file":
    eventName = "media_message"
case "voice":
    eventName = "media_message"
case "video":
    eventName = "media_message"
default:
    eventName = "new_message" // 默认事件名称
}
```

### 2. 更新文档格式
更新了 `WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md` 中的消息格式示例，使其与实际的数据结构一致：

- 添加了 `session_id` 和 `timestamp` 字段
- 使用完整的MessageDTO数据结构
- 包含发送者的详细信息（name, avatar等）
- 包含消息的完整元数据（id, created_at, status等）

### 3. 保持向后兼容
- 保留了原有的数据结构，确保现有前端代码不会受到影响
- 只修改了事件名称，使其更加语义化和一致

## 📋 修改的文件

1. **modules/chat/controllers/message_controller.go**
   - 修改 `pushMessageToWebSocket` 方法
   - 根据消息类型设置正确的事件名称

2. **WEBSOCKET_MESSAGE_BROADCAST_GUIDE.md**
   - 更新文本消息和媒体消息的示例格式
   - 使用真实的数据结构和字段名称

## ✅ 修复效果

### 修复前
- 事件名称不一致（`new_message` vs `text_message`）
- 文档与实际实现不匹配
- 前端开发者困惑

### 修复后
- 统一的事件名称体系：
  - 文本消息：`text_message`
  - 媒体消息：`media_message`
  - 系统消息：`system_message`
- 文档与实际实现完全一致
- 前端可以根据事件类型进行精确的消息处理

## 🎯 前端对接建议

### 消息处理逻辑
```javascript
websocket.onmessage = function(event) {
    const message = JSON.parse(event.data);
    
    switch(message.event) {
        case 'text_message':
            handleTextMessage(message.data);
            break;
        case 'media_message':
            handleMediaMessage(message.data);
            break;
        case 'system_message':
            handleSystemMessage(message.data);
            break;
        default:
            console.log('未知消息类型:', message.event);
    }
};
```

### 数据字段说明
前端可以直接使用 `message.data` 中的以下字段：
- `id`: 消息ID
- `session_id`: 会话ID
- `sender_id`: 发送者ID
- `sender_type`: 发送者类型（user/merchant/admin）
- `sender_name`: 发送者姓名
- `sender_avatar`: 发送者头像URL
- `content`: 消息内容
- `type`: 消息类型（text/image/file/voice/video）
- `created_at`: 创建时间

## 🔄 后续建议

1. **测试验证**：建议进行完整的端到端测试，确保所有消息类型都能正确发送和接收
2. **文档维护**：后续如有新的消息类型，请同时更新代码和文档
3. **监控告警**：建议添加WebSocket消息发送的监控，及时发现格式不一致的问题
