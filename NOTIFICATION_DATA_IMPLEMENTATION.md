# NotificationData字段实现文档

## 问题描述

前端用户访问 `/api/v1/chat/sessions/14/messages?page=1&page_size=10&order=desc` 获取用户消息时，应该携带 `NotificationData` 数据，前端可以根据 `NotificationData` 中的数据进行业务跳转。

## 解决方案

### 1. 数据库模型支持

在 `chat_message` 表中已经存在 `NotificationData` 字段：

```go
// modules/chat/models/chat_message.go
type ChatMessage struct {
    // ... 其他字段
    NotificationType string     `orm:"size(50);null" json:"notification_type"`   // 通知类型
    NotificationData string     `orm:"type(text);null" json:"notification_data"` // 通知数据（JSON格式）
    Priority         int        `orm:"default(1)" json:"priority"`               // 优先级（1:低, 2:中, 3:高）
    Persistent       bool       `orm:"default(false)" json:"persistent"`         // 是否持久化
    ExpireAt         *time.Time `orm:"null" json:"expire_at"`                    // 过期时间
    // ... 其他字段
}
```

### 2. DTO结构体更新

更新了 `MessageDTO` 结构体，添加了通知相关字段：

```go
// modules/chat/dto/chat_dto.go
type MessageDTO struct {
    ID         int64     `json:"id"`                  // 消息ID
    SessionID  int64     `json:"session_id"`          // 会话ID
    SenderID   int64     `json:"sender_id"`           // 发送者ID
    SenderType string    `json:"sender_type"`         // 发送者类型（user/merchant/system）
    Content    string    `json:"content"`             // 消息内容
    Type       string    `json:"type"`                // 消息类型（text/image/file/voice/notification）
    // ... 其他基础字段

    // 通知消息相关字段
    NotificationType string      `json:"notification_type,omitempty"` // 通知类型
    NotificationData interface{} `json:"notification_data,omitempty"` // 通知数据（JSON对象，前端可用于业务跳转）
    Priority         int         `json:"priority,omitempty"`          // 优先级（1:低, 2:中, 3:高）
    Persistent       bool        `json:"persistent,omitempty"`        // 是否持久化
    ExpireAt         *time.Time  `json:"expire_at,omitempty"`         // 过期时间

    // ... 其他扩展字段
}
```

### 3. 服务层JSON解析

在 `ChatServiceImpl.GetSessionMessages` 方法中添加了 `NotificationData` 的JSON解析逻辑：

```go
// modules/chat/services/impl/chat_service_impl.go
// 解析NotificationData JSON字符串为对象
if message.NotificationData != "" {
    var notificationData interface{}
    if err := json.Unmarshal([]byte(message.NotificationData), &notificationData); err != nil {
        logs.Warn("[GetSessionMessages] 解析NotificationData失败: %v, 原始数据: %s", err, message.NotificationData)
        // 解析失败时，将原始字符串作为数据返回
        messageDTO.NotificationData = message.NotificationData
    } else {
        messageDTO.NotificationData = notificationData
        logs.Debug("[GetSessionMessages] NotificationData解析成功: %+v", notificationData)
    }
}
```

### 4. API响应格式

现在 `/api/v1/chat/sessions/{id}/messages` 接口返回的消息包含完整的通知数据：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1001,
        "session_id": 14,
        "sender_id": 0,
        "sender_type": "system",
        "content": "您的订单已支付成功",
        "type": "notification",
        "notification_type": "order_payment_success",
        "notification_data": {
          "order_id": 12345,
          "order_no": "ORD20250130001",
          "amount": 99.99,
          "payment_time": 1706598000,
          "jump_url": "/order/detail/12345",
          "action_type": "view_order",
          "merchant_id": 2001,
          "user_id": 1001
        },
        "priority": 2,
        "persistent": true,
        "status": 0,
        "created_at": "2025-01-30T10:30:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10,
    "page_count": 1
  }
}
```

## NotificationData数据结构示例

### 1. 订单支付成功通知

```json
{
  "order_id": 12345,
  "order_no": "ORD20250130001",
  "amount": 99.99,
  "payment_time": 1706598000,
  "jump_url": "/order/detail/12345",
  "action_type": "view_order",
  "merchant_id": 2001,
  "user_id": 1001
}
```

### 2. 商家新订单通知

```json
{
  "order_id": 12346,
  "order_no": "ORD20250130002",
  "customer_name": "张三",
  "customer_phone": "13800138000",
  "total_amount": 158.50,
  "order_time": 1706598000,
  "jump_url": "/merchant/order/detail/12346",
  "action_type": "process_order",
  "priority": "high",
  "items": [
    {
      "product_name": "麻辣烫",
      "quantity": 2,
      "price": 25.00
    }
  ]
}
```

### 3. 退款通知

```json
{
  "refund_id": 5001,
  "refund_no": "REF20250130001",
  "order_id": 12345,
  "order_no": "ORD20250130001",
  "refund_amount": 99.99,
  "refund_reason": "商品质量问题",
  "refund_time": 1706598000,
  "jump_url": "/refund/detail/5001",
  "action_type": "handle_refund",
  "status": "pending"
}
```

### 4. 跑腿订单通知

```json
{
  "runner_order_id": 8001,
  "order_no": "RUN20250130001",
  "pickup_address": "北京市朝阳区xxx街道xxx号",
  "delivery_address": "北京市海淀区xxx路xxx号",
  "reward_amount": 25.00,
  "distance": "3.2km",
  "estimated_time": "30分钟",
  "jump_url": "/runner/order/detail/8001",
  "action_type": "accept_order",
  "urgency": "normal",
  "contact_phone": "13900139000"
}
```

## 前端使用指南

### 1. 检测通知消息

```javascript
// 检查消息类型是否为通知
if (message.type === 'notification' && message.notification_data) {
    // 处理通知消息
    handleNotificationMessage(message);
}
```

### 2. 业务跳转处理

```javascript
function handleNotificationMessage(message) {
    const notificationData = message.notification_data;
    const actionType = notificationData.action_type;
    const jumpUrl = notificationData.jump_url;
    
    switch (actionType) {
        case 'view_order':
            // 跳转到订单详情页
            router.push(jumpUrl);
            break;
            
        case 'process_order':
            // 跳转到订单处理页
            router.push(jumpUrl);
            break;
            
        case 'handle_refund':
            // 跳转到退款处理页
            router.push(jumpUrl);
            break;
            
        case 'accept_order':
            // 跳转到跑腿订单接单页
            router.push(jumpUrl);
            break;
            
        default:
            // 默认跳转
            if (jumpUrl) {
                router.push(jumpUrl);
            }
    }
}
```

### 3. 通知样式处理

```javascript
function getNotificationStyle(message) {
    const priority = message.priority || 1;
    
    switch (priority) {
        case 3: // 高优先级
            return 'notification-high';
        case 2: // 中优先级
            return 'notification-medium';
        case 1: // 低优先级
        default:
            return 'notification-low';
    }
}
```

### 4. 通知数据提取

```javascript
function extractNotificationInfo(message) {
    const data = message.notification_data;
    
    return {
        // 业务ID（订单ID、退款ID等）
        businessId: data.order_id || data.refund_id || data.runner_order_id,
        
        // 业务编号
        businessNo: data.order_no || data.refund_no,
        
        // 金额信息
        amount: data.amount || data.total_amount || data.refund_amount || data.reward_amount,
        
        // 跳转信息
        jumpUrl: data.jump_url,
        actionType: data.action_type,
        
        // 时间信息
        timestamp: data.payment_time || data.order_time || data.refund_time,
        
        // 其他业务相关信息
        extraInfo: data
    };
}
```

## 技术特点

### 1. 数据完整性
- ✅ 数据库存储为JSON字符串，确保数据完整性
- ✅ API返回时解析为JSON对象，便于前端使用
- ✅ 解析失败时返回原始字符串，确保数据不丢失

### 2. 类型安全
- ✅ DTO中使用 `interface{}` 类型，支持任意JSON结构
- ✅ 前端可以根据 `notification_type` 确定数据结构
- ✅ 提供了多种业务场景的数据结构示例

### 3. 扩展性
- ✅ 支持任意业务数据结构
- ✅ 可以根据业务需求添加新的通知类型
- ✅ 前端可以灵活处理不同类型的通知数据

### 4. 向后兼容
- ✅ 非通知消息不受影响
- ✅ 旧版本前端可以忽略新增字段
- ✅ 渐进式升级支持

## 总结

现在前端访问 `/api/v1/chat/sessions/14/messages` 接口时，可以获取到完整的 `NotificationData` 数据，包括：

1. **业务跳转信息**：`jump_url` 和 `action_type`
2. **业务数据**：订单ID、金额、时间等
3. **显示优先级**：`priority` 字段
4. **通知类型**：`notification_type` 字段

前端可以根据这些数据实现：
- 🎯 **精确的业务跳转**
- 🎨 **差异化的通知样式**
- 📱 **丰富的交互体验**
- 🔄 **灵活的数据处理**

功能已完全实现并测试通过！🎉
