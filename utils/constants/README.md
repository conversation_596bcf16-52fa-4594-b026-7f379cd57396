# 统一常量定义

本目录包含项目中所有模块使用的统一常量定义，旨在解决各模块间常量不一致的问题，提高代码的可维护性。

## 📁 文件结构

- `business.go` - 业务相关常量（订单状态、配送状态等）
- `payment.go` - 支付相关常量（支付方式、支付状态、退款状态等）
- `system.go` - 系统相关常量（错误码、用户类型等）

## 🎯 设计原则

### 1. 统一性
所有模块使用相同的常量值，避免定义不一致导致的问题。

### 2. 可维护性
集中管理常量定义，修改时只需要在一个地方进行。

### 3. 向后兼容
保持现有API接口不变，只是底层使用统一的常量定义。

### 4. 清晰性
使用有意义的常量名称和完整的注释说明。

## 📝 使用规范

### 导入方式
```go
import "o_mall_backend/utils/constants"
```

### 命名规范
- **业务常量**：以业务领域为前缀，如 `OrderStatus`、`PaymentStatus`
- **枚举值**：使用有意义的后缀，如 `OrderStatusPending`、`PaymentStatusSuccess`
- **映射表**：以 `Map` 为后缀，如 `OrderStatusMap`、`PaymentMethodMap`

### 示例用法
```go
// 检查订单状态
if order.Status == constants.OrderStatusPaid {
    // 处理已付款订单
}

// 获取状态描述
statusText := constants.OrderStatusMap[order.Status]

// 检查支付方式
if payment.Method == constants.PaymentMethodBalance {
    // 处理余额支付
}
```

## 🔄 迁移指南

### 第一阶段：创建统一常量
1. 定义所有业务常量
2. 创建常量映射表
3. 添加完整的文档注释

### 第二阶段：模块迁移
1. 各模块逐步引用统一常量
2. 保持原有常量定义（标记为废弃）
3. 更新相关业务逻辑

### 第三阶段：清理工作
1. 移除各模块中的重复定义
2. 更新相关文档
3. 进行全面测试

## ⚠️ 注意事项

1. **常量值不可随意修改**：一旦定义，常量值应保持稳定，避免影响数据库中的历史数据
2. **新增常量**：新增常量时需要考虑与现有常量的兼容性
3. **废弃常量**：废弃常量时应添加注释说明，并提供替代方案
4. **测试验证**：修改常量后需要进行充分的测试验证

## 📚 参考文档

- [Go 常量最佳实践](https://golang.org/doc/effective_go.html#constants)
- [项目编码规范](../docs/coding_standards.md)
