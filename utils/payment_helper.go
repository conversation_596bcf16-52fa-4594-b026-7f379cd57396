/**
 * payment_helper.go
 * 支付相关辅助工具函数
 *
 * 本文件提供支付相关的通用辅助函数，确保支付方式映射的一致性。
 * 避免在各个模块中重复定义支付方式映射逻辑。
 */

package utils

import "o_mall_backend/utils/constants"

// GetPaymentMethodText 获取支付方式文本描述
// 使用统一常量包的映射表，确保全系统一致性
func GetPaymentMethodText(payMethod int) string {
	// 使用统一常量包的支付方式映射表
	if text, ok := constants.PaymentMethodMap[payMethod]; ok {
		return text
	}
	// 如果未找到，检查是否为未选择状态
	if payMethod == 0 {
		return "未选择"
	}
	return "未知支付方式"
}

// GetPaymentStatusText 获取支付状态文本描述
// 使用统一常量包的映射表，确保全系统一致性
func GetPaymentStatusText(payStatus int) string {
	// 使用统一常量包的支付状态映射表
	if text, ok := constants.PaymentStatusMap[payStatus]; ok {
		return text
	}
	return "未知状态"
}

// ValidatePaymentMethod 验证支付方式是否有效
func ValidatePaymentMethod(payMethod int) bool {
	// 检查是否在有效的支付方式范围内
	_, exists := constants.PaymentMethodMap[payMethod]
	return exists || payMethod == 0 // 0表示未选择，也是有效的
}

// GetSupportedPaymentMethods 获取系统支持的所有支付方式
func GetSupportedPaymentMethods() map[int]string {
	// 返回统一常量包中定义的支付方式映射表的副本
	result := make(map[int]string)
	for k, v := range constants.PaymentMethodMap {
		result[k] = v
	}
	// 添加未选择状态
	result[0] = "未选择"
	return result
}

// ConvertPaymentMethodStringToInt 将支付方式字符串转换为整数常量
func ConvertPaymentMethodStringToInt(payMethodStr string) int {
	switch payMethodStr {
	case "wechat", "微信支付":
		return constants.PaymentMethodWechat
	case "alipay", "支付宝":
		return constants.PaymentMethodAlipay
	case "balance", "余额支付":
		return constants.PaymentMethodBalance
	case "creditcard", "信用卡支付":
		return constants.PaymentMethodCreditCard
	case "banktransfer", "银行转账":
		return constants.PaymentMethodBankTransfer
	case "combination", "组合支付":
		return constants.PaymentMethodCombination
	default:
		return constants.PaymentMethodUnknown
	}
}

// IsBalancePayment 判断是否为余额支付
func IsBalancePayment(payMethod int) bool {
	return payMethod == constants.PaymentMethodBalance
}

// IsWechatPayment 判断是否为微信支付
func IsWechatPayment(payMethod int) bool {
	return payMethod == constants.PaymentMethodWechat
}

// IsAlipayPayment 判断是否为支付宝支付
func IsAlipayPayment(payMethod int) bool {
	return payMethod == constants.PaymentMethodAlipay
}
