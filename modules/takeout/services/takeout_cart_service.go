/**
 * 外卖购物车服务
 *
 * 本文件实现了外卖购物车的业务逻辑层，处理外卖商品添加到购物车、更新购物车等操作。
 * 与现有购物车系统集成，支持规格选择和套餐组合功能。
 */

package services

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	cartdto "o_mall_backend/modules/cart/dto" // 使用别名解决包冲突
	cartservices "o_mall_backend/modules/cart/services"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"
)

// TakeoutCartService 外卖购物车服务接口
type TakeoutCartService interface {
	// 购物车操作
	AddToCart(userID int64, req *dto.AddTakeoutToCartRequest) (int64, error)
	UpdateCart(userID int64, req *dto.UpdateTakeoutCartRequest) error
	UpdateCartItem(userID int64, req *dto.UpdateTakeoutCartItemRequest) error
	RemoveFromCart(userID int64, cartItemID int64) error
	BatchRemoveFromCart(userID int64, cartItemIDs []int64) error
	GetCartItemDetails(userID int64, cartItemID int64) (*dto.TakeoutCartItemDTO, error)
	SelectCartItem(userID int64, req *dto.SelectCartItemRequest) error

	// 购物车查询
	ListCartItems(userID int64) ([]dto.TakeoutCartItemDTO, error)
	GetCartItems(userID int64) ([]dto.TakeoutCartItemDTO, error)
	CountCartItems(userID int64) (int, error)
	CountCartItemsWithDetails(userID int64) (*dto.CartCountDetailsDTO, error)

	// 购物车结算
	GetCartSummary(userID int64, cartItemIDs []int64) (*dto.TakeoutCartSummaryDTO, error)
	CheckoutCart(userID int64, cartItemIDs []int64, addressID int64) (*dto.TakeoutCartCheckoutDTO, error)

	// 购物车验证
	ValidateCartItems(userID int64) ([]int64, error)
	ClearInvalidCartItems(userID int64) error
}

// takeoutCartService 外卖购物车服务实现
type takeoutCartService struct {
	cartRepo    repositories.TakeoutCartRepository
	foodRepo    repositories.TakeoutFoodRepository
	variantRepo repositories.TakeoutVariantRepository
	comboRepo   repositories.TakeoutComboRepository
	baseCartSvc cartservices.CartService // 集成原有购物车服务
	comboSvc    TakeoutComboService
	cacheSvc    TakeoutCartCacheService
}

// NewTakeoutCartService 创建外卖购物车服务实例
func NewTakeoutCartService() TakeoutCartService {
	return &takeoutCartService{
		cartRepo:    repositories.NewTakeoutCartRepository(),
		foodRepo:    repositories.NewTakeoutFoodRepository(),
		variantRepo: repositories.NewTakeoutVariantRepository(),
		comboRepo:   repositories.NewTakeoutComboRepository(),
		baseCartSvc: cartservices.NewCartService(),
		comboSvc:    NewTakeoutComboService(),
		cacheSvc:    NewTakeoutCartCacheService(),
	}
}

// updateCartCacheAfterModification 统一的购物车缓存更新策略
// 在购物车修改操作后调用，确保缓存与数据库数据一致
func (s *takeoutCartService) updateCartCacheAfterModification(userID int64, operation string) {
	logs.Info("开始更新购物车缓存, 用户ID: %d, 操作: %s", userID, operation)

	// 先删除缓存，确保清除旧数据
	if err := s.cacheSvc.DeleteCartCache(userID); err != nil {
		logs.Warn("删除购物车缓存失败: %v, 用户ID: %d, 操作: %s", err, userID, operation)
	} else {
		logs.Debug("购物车缓存删除成功, 用户ID: %d, 操作: %s", userID, operation)
	}

	// 立即刷新缓存，确保前端能获取到最新数据
	if err := s.cacheSvc.RefreshCartCache(userID, s.ListCartItems); err != nil {
		logs.Error("刷新购物车缓存失败: %v, 用户ID: %d, 操作: %s", err, userID, operation)
	} else {
		logs.Info("购物车缓存刷新成功, 用户ID: %d, 操作: %s", userID, operation)
	}

	// 异步再次刷新缓存，确保数据完全同步
	go func(uid int64, op string) {
		// 等待小段时间，让数据库操作完全生效
		time.Sleep(100 * time.Millisecond)
		if err := s.cacheSvc.RefreshCartCache(uid, s.ListCartItems); err != nil {
			logs.Warn("异步刷新购物车缓存失败: %v, 用户ID: %d, 操作: %s", err, uid, op)
		} else {
			logs.Debug("异步刷新购物车缓存成功, 用户ID: %d, 操作: %s", uid, op)
		}
	}(userID, operation)
}

// AddToCart 添加外卖商品到购物车
func (s *takeoutCartService) AddToCart(userID int64, req *dto.AddTakeoutToCartRequest) (int64, error) {
	// 获取食品信息
	foodResult, err := s.foodRepo.GetByID(req.FoodID)
	if err != nil {
		return 0, err
	}

	// 显式声明为正确的模型类型
	food := foodResult

	// 检查食品状态
	if food == nil {
		return 0, errors.New("食品信息不存在")
	}

	// 检查食品上架状态 - 使用显式字段访问
	currentStatus := food.Status
	if currentStatus != models.FoodStatusOnSale {
		return 0, errors.New("该商品未上架或已下架")
	}

	// 检查是否售罄
	isSoldOut := food.SoldOut
	if isSoldOut {
		return 0, errors.New("该商品已售罄")
	}

	// 处理变体规格
	var variant *models.TakeoutFoodVariant
	var variantName string
	var price float64 = food.Price

	hasVariants := food.HasVariants
	if hasVariants {
		if req.VariantID <= 0 {
			return 0, errors.New("请选择规格")
		}

		// 获取变体信息
		var variantResult *models.TakeoutFoodVariant
		variantResult, err = s.variantRepo.GetByID(req.VariantID)
		if err != nil {
			return 0, err
		}
		variant = variantResult // 类型转换和分配

		// 检查变体是否属于该食品
		variantFoodID := variant.FoodID
		foodID := food.ID
		if variantFoodID != foodID {
			return 0, errors.New("所选规格不属于该商品")
		}

		variantName = variant.Name
		price = variant.Price

		// 检查库存
		variantStock := variant.Stock
		if variantStock > 0 && variantStock < req.Quantity {
			return 0, errors.New("所选规格库存不足")
		}
	}

	// 检查购物车中是否已存在相同商品和规格
	// 通过基础购物车API查询
	ctxCart := context.Background()
	cartResponse, cartErr := s.baseCartSvc.GetCart(ctxCart, userID, "")

	// 查询购物车出错不阻止添加流程
	if cartErr == nil && cartResponse != nil && len(cartResponse.Items) > 0 {
		// 检查是否有相同商品
		for _, item := range cartResponse.Items {
			// 查找是否有相同商品ID和SKU ID的商品
			if item.ProductID == food.ID && item.SkuID == req.VariantID {
				// 检查外卖购物车项是否还存在
				_, extErr := s.cartRepo.GetCartItemByCartItemID(item.ID)
				if extErr == nil {
					// 外卖购物车项仍然存在，更新数量
					ctxUpdate := context.Background()
					selected := true
					updateErr := s.baseCartSvc.UpdateCartItem(ctxUpdate, &cartdto.UpdateCartItemRequest{
						ID:       item.ID,
						UserID:   userID,
						Quantity: item.Quantity + req.Quantity,
						Selected: &selected,
					})

					if updateErr != nil {
						logs.Error("更新已有购物车项数量失败: %v, 购物车项ID: %d", updateErr, item.ID)
						return 0, errors.New("添加购物车失败")
					}

					// 记录操作日志
					log := &models.TakeoutCartItemLog{
						UserID:       userID,
						CartItemID:   item.ID,
						FoodID:       food.ID,
						VariantID:    req.VariantID,
						Action:       "update",
						Quantity:     item.Quantity + req.Quantity,
						IP:           "",
						ComboChanged: false,
					}

					_, _ = s.cartRepo.CreateCartItemLog(log)
					return item.ID, nil
				} else {
					// 外卖购物车项不存在，但基础购物车项存在，说明存在不一致
					// 清理无效的基础购物车项
					ctxDelete := context.Background()
					_ = s.baseCartSvc.DeleteCartItem(ctxDelete, &cartdto.DeleteCartItemRequest{
						ID:     item.ID,
						UserID: userID,
					})
					// 彻底删除
					_, _ = s.cartRepo.HardDeleteCartItem(item.ID, userID)
				}
			}
		}
	}

	// 处理套餐组合
	var comboSelectData string

	isCombination := food.IsCombination
	if isCombination {
		// 需要将CartComboSelectionRequest转换为ComboSelectionRequest
		comboSelections := s.convertCartToComboSelections(req.ComboSelections)

		// 验证套餐选择是否有效
		err = s.comboSvc.ValidateComboSelections(food.ID, comboSelections)
		if err != nil {
			return 0, err
		}

		// 计算套餐价格
		totalPrice, err := s.comboSvc.CalculateComboPrice(food.ID, comboSelections)
		if err != nil {
			return 0, err
		}

		// 更新商品价格为套餐总价
		price = totalPrice

		// 转换为存储格式
		comboSelectData, err = s.convertComboSelectionsToJson(comboSelections)
		if err != nil {
			return 0, err
		}
	}

	// 创建基础购物车项
	ctx := context.Background()
	response, err := s.baseCartSvc.AddToCart(ctx, &cartdto.AddToCartRequest{
		UserID:       userID,
		ProductID:    food.ID,
		SkuID:        req.VariantID,
		Quantity:     req.Quantity,
		Price:        price,
		ProductName:  food.Name,  // 添加商品名称
		ProductImage: food.Image, // 添加商品图片
		Selected:     true,
	})

	var cartItemID int64
	if response != nil {
		cartItemID = response.ID
	}

	if err != nil {
		logs.Error("添加基础购物车项失败: %v, 用户ID: %d, 请求: %+v", err, userID, req)
		return 0, errors.New("添加购物车失败")
	}

	// 创建外卖购物车扩展项
	takeoutCartItem := &models.TakeoutCartItem{
		CartItemID:      cartItemID,
		FoodID:          food.ID,
		VariantID:       req.VariantID,
		VariantName:     variantName,
		PackagingFee:    food.PackagingFee,
		ComboSelectData: comboSelectData,
		Remark:          req.Remark,
	}

	_, err = s.cartRepo.CreateCartItem(takeoutCartItem)
	if err != nil {
		logs.Error("创建外卖购物车项失败: %v, 购物车项ID: %d", err, cartItemID)
		// 回滚基础购物车项
		ctx := context.Background()
		_ = s.baseCartSvc.DeleteCartItem(ctx, &cartdto.DeleteCartItemRequest{
			ID:     cartItemID,
			UserID: userID,
		})
		return 0, errors.New("添加购物车失败")
	}

	// 记录操作日志
	log := &models.TakeoutCartItemLog{
		UserID:       userID,
		CartItemID:   cartItemID,
		FoodID:       food.ID,
		VariantID:    req.VariantID,
		Action:       "add",
		Quantity:     req.Quantity,
		IP:           "", // 在控制器层填充
		ComboChanged: len(req.ComboSelections) > 0,
	}

	_, _ = s.cartRepo.CreateCartItemLog(log)

	// 统一的缓存更新策略
	s.updateCartCacheAfterModification(userID, "add")

	return cartItemID, nil
}

// UpdateCartItem 更新购物车项
func (s *takeoutCartService) UpdateCartItem(userID int64, req *dto.UpdateTakeoutCartItemRequest) error {
	// 获取外卖购物车项
	cartItem, err := s.cartRepo.GetCartItemByCartItemID(req.CartItemID)
	if err != nil {
		return err
	}

	// 获取食品信息
	food, err := s.foodRepo.GetByID(cartItem.FoodID)
	if err != nil {
		return err
	}

	// 更新套餐组合选择
	if food.IsCombination && len(req.ComboSelections) > 0 {
		// 验证套餐选择是否有效
		err = s.comboSvc.ValidateComboSelections(food.ID, req.ComboSelections)
		if err != nil {
			return err
		}

		// 转换为存储格式
		comboSelectData, err := s.convertComboSelectionsToJson(req.ComboSelections)
		if err != nil {
			return err
		}

		cartItem.ComboSelectData = comboSelectData
	}

	// 更新备注
	if req.Remark != "" {
		cartItem.Remark = req.Remark
	}

	// 更新外卖购物车项
	err = s.cartRepo.UpdateCartItem(cartItem)
	if err != nil {
		logs.Error("更新外卖购物车项失败: %v, 购物车项ID: %d", err, req.CartItemID)
		return errors.New("更新购物车失败")
	}

	// 更新基础购物车项数量
	ctx := context.Background()
	selected := true // 默认选中状态
	err = s.baseCartSvc.UpdateCartItem(ctx, &cartdto.UpdateCartItemRequest{
		ID:       req.CartItemID,
		UserID:   userID,
		Quantity: req.Quantity,
		Selected: &selected,
	})
	if err != nil {
		logs.Error("更新基础购物车项数量失败: %v, 购物车项ID: %d", err, req.CartItemID)
		return errors.New("更新购物车失败")
	}

	// 记录操作日志
	log := &models.TakeoutCartItemLog{
		UserID:       userID,
		CartItemID:   req.CartItemID,
		FoodID:       cartItem.FoodID,
		VariantID:    cartItem.VariantID,
		Action:       "update",
		Quantity:     req.Quantity,
		IP:           "", // 在控制器层填充
		ComboChanged: len(req.ComboSelections) > 0,
	}

	_, _ = s.cartRepo.CreateCartItemLog(log)

	// 强制删除缓存，确保下次查询获取最新数据
	if err := s.cacheSvc.DeleteCartCache(userID); err != nil {
		logs.Warn("删除购物车缓存失败: %v, 用户ID: %d", err, userID)
	} else {
		logs.Info("购物车缓存删除成功, 用户ID: %d", userID)
	}

	// 等待一小段时间确保数据库操作完全生效
	time.Sleep(50 * time.Millisecond)

	// 立即刷新缓存，确保数据一致性
	logs.Info("开始立即刷新购物车缓存, 用户ID: %d", userID)
	if err := s.cacheSvc.RefreshCartCache(userID, func(uid int64) ([]dto.TakeoutCartItemDTO, error) {
		logs.Info("RefreshCartCache调用ListCartItems获取最新数据, 用户ID: %d", uid)
		items, err := s.ListCartItems(uid)
		if err != nil {
			logs.Error("RefreshCartCache中ListCartItems失败: %v, 用户ID: %d", err, uid)
		} else {
			logs.Info("RefreshCartCache中ListCartItems成功, 用户ID: %d, 条目数: %d", uid, len(items))
		}
		return items, err
	}); err != nil {
		logs.Warn("立即刷新购物车缓存失败: %v, 用户ID: %d", err, userID)
	} else {
		logs.Info("立即刷新购物车缓存成功, 用户ID: %d", userID)
	}

	// 再次等待确保缓存写入完成
	time.Sleep(50 * time.Millisecond)

	// 异步再次刷新缓存，确保彻底更新
	go func(uid int64) {
		// 等待小段时间，让数据库操作完全生效
		time.Sleep(100 * time.Millisecond)
		if err := s.cacheSvc.RefreshCartCache(uid, func(uid int64) ([]dto.TakeoutCartItemDTO, error) {
			return s.ListCartItems(uid)
		}); err != nil {
			logs.Warn("异步刷新购物车缓存失败: %v, 用户ID: %d", err, uid)
		} else {
			logs.Info("异步刷新购物车缓存成功, 用户ID: %d", uid)
		}
	}(userID)

	return nil
}

// RemoveFromCart 从购物车移除商品
func (s *takeoutCartService) RemoveFromCart(userID int64, cartItemID int64) error {
	// 获取外卖购物车项
	cartItem, err := s.cartRepo.GetCartItemByCartItemID(cartItemID)
	if err != nil {
		if err == orm.ErrNoRows {
			// 如果外卖购物车扩展项不存在，尝试删除基础购物车项
			ctx := context.Background()
			// 尝试删除基础购物车项
			_ = s.baseCartSvc.DeleteCartItem(ctx, &cartdto.DeleteCartItemRequest{
				ID:     cartItemID,
				UserID: userID,
			})
			// 确保彻底删除基础购物车项
			_, _ = s.cartRepo.HardDeleteCartItem(cartItemID, userID)
			return nil
		}
		return err
	}

	// 记录食品信息用于日志
	foodID := cartItem.FoodID
	variantID := cartItem.VariantID

	// 记录操作日志（在删除前记录）
	log := &models.TakeoutCartItemLog{
		UserID:       userID,
		CartItemID:   cartItemID,
		FoodID:       foodID,
		VariantID:    variantID,
		Action:       "delete",
		Quantity:     0,
		IP:           "", // 在控制器层填充
		ComboChanged: false,
	}

	_, _ = s.cartRepo.CreateCartItemLog(log)

	// 删除外卖购物车项
	err = s.cartRepo.DeleteCartItem(cartItemID)
	if err != nil {
		logs.Error("删除外卖购物车项失败: %v, 购物车项ID: %d", err, cartItemID)
		return errors.New("移除购物车失败")
	}

	// 使用硬删除方法彻底删除基础购物车项
	ctx := context.Background()
	// 先尝试标准删除方法
	err = s.baseCartSvc.DeleteCartItem(ctx, &cartdto.DeleteCartItemRequest{
		ID:     cartItemID,
		UserID: userID,
	})
	if err != nil {
		logs.Error("删除基础购物车项失败: %v, 购物车项ID: %d", err, cartItemID)
		// 不返回错误，继续尝试硬删除
	}

	// 确保基础购物车项被彻底删除，直接执行SQL删除
	// rows, err := s.cartRepo.HardDeleteCartItem(cartItemID, userID)
	// if err != nil {
	// 	logs.Error("硬删除基础购物车项失败: %v, 购物车项ID: %d", err, cartItemID)
	// 	return errors.New("移除购物车失败")
	// }

	//logs.Info("成功硬删除购物车项，影响行数: %d, 购物车项ID: %d", rows, cartItemID)

	// 统一的缓存更新策略
	s.updateCartCacheAfterModification(userID, "remove")

	return nil
}

// GetCartItemDetails 获取购物车项详情
// 新增用户ID参数，确保正确获取对应用户的购物车项
func (s *takeoutCartService) GetCartItemDetails(userID int64, cartItemID int64) (*dto.TakeoutCartItemDTO, error) {
	// 记录日志，记录当前正在查询的用户ID和购物车项ID
	logs.Info("查询购物车项详情: 用户ID=%d, 购物车项ID=%d", userID, cartItemID)

	// 获取外卖购物车项
	takeoutCartItem, err := s.cartRepo.GetCartItemByCartItemID(cartItemID)
	if err != nil {
		logs.Warn("获取购物车项失败: %v, 购物车项ID=%d", err, cartItemID)
		return nil, err
	}

	// 使用传入的用户ID获取购物车
	ctx := context.Background()
	cart, err := s.baseCartSvc.GetCart(ctx, userID, "")
	if err != nil {
		logs.Warn("获取购物车失败: %v, 用户ID=%d", err, userID)
		return nil, err
	}

	// 从购物车中找到对应的购物车项
	var baseCartItem *cartdto.CartItemResponse
	for _, item := range cart.Items {
		if item.ID == cartItemID {
			baseCartItem = item
			break
		}
	}

	if baseCartItem == nil {
		return nil, errors.New("购物车项不存在")
	}

	// 获取食品信息
	food, err := s.foodRepo.GetByID(takeoutCartItem.FoodID)
	if err != nil {
		return nil, err
	}

	// 获取变体信息
	var variantPrice float64 = food.Price
	if takeoutCartItem.VariantID > 0 {
		variant, err := s.variantRepo.GetByID(takeoutCartItem.VariantID)
		if err == nil {
			variantPrice = variant.Price
		}
	}

	// 添加调试信息：检查ProductImage字段
	logs.Info("[调试] GetCartItemDetails - 购物车项ID: %d, baseCartItem.ProductImage: '%s', food.Image: '%s'",
		cartItemID, baseCartItem.ProductImage, food.Image)

	// 如果baseCartItem.ProductImage为空，尝试使用food.Image
	foodImage := baseCartItem.ProductImage
	if foodImage == "" && food.Image != "" {
		foodImage = food.Image
		logs.Info("[调试] ProductImage为空，使用food.Image: '%s'", foodImage)
	}

	// 构造购物车项DTO
	cartItemDTO := &dto.TakeoutCartItemDTO{
		CartItemID:    cartItemID,
		FoodID:        takeoutCartItem.FoodID,
		FoodName:      baseCartItem.ProductName,
		FoodImage:     foodImage,
		VariantID:     takeoutCartItem.VariantID,
		VariantName:   takeoutCartItem.VariantName,
		Price:         variantPrice,
		OriginalPrice: food.OriginalPrice,
		Quantity:      baseCartItem.Quantity,
		PackagingFee:  takeoutCartItem.PackagingFee,
		Subtotal:      variantPrice * float64(baseCartItem.Quantity),
		Remark:        takeoutCartItem.Remark,
		Selected:      baseCartItem.Selected,
	}

	// 处理套餐组合选择
	if food.IsCombination && takeoutCartItem.ComboSelectData != "" {
		comboSelections, err := s.parseComboSelectionsFromJson(takeoutCartItem.ComboSelectData)
		if err == nil {
			cartItemDTO.ComboSelections = comboSelections
		}
	}

	return cartItemDTO, nil
}

// ListCartItems, 查询用户购物车列表
func (s *takeoutCartService) ListCartItems(userID int64) ([]dto.TakeoutCartItemDTO, error) {
	// 获取用户购物车
	ctx := context.Background()
	cart, err := s.baseCartSvc.GetCart(ctx, userID, "")
	if err != nil {
		return nil, err
	}

	// 获取购物车项列表
	cartItems := cart.Items

	// 获取所有购物车项ID
	var takeoutCartItemIDs []int64
	for _, item := range cartItems {
		takeoutCartItemIDs = append(takeoutCartItemIDs, item.ID)
	}

	if len(takeoutCartItemIDs) == 0 {
		return []dto.TakeoutCartItemDTO{}, nil
	}

	// 获取外卖购物车项
	takeoutItems, err := s.cartRepo.ListCartItemsByCartIDs(takeoutCartItemIDs)
	if err != nil {
		return nil, err
	}

	// 将外卖购物车项映射为map，便于查找
	takeoutItemMap := make(map[int64]*models.TakeoutCartItem)
	for _, item := range takeoutItems {
		takeoutItemMap[item.CartItemID] = item
	}

	// 构造购物车项DTO列表
	result := make([]dto.TakeoutCartItemDTO, 0, len(takeoutCartItemIDs))

	logs.Info("ListCartItems开始构造DTO列表, 用户ID: %d, 基础购物车项数: %d", userID, len(cartItems))

	for _, cartItem := range cartItems {

		takeoutItem, ok := takeoutItemMap[cartItem.ID]
		if !ok {
			continue
		}

		// 获取食品信息（包含商家信息）
		food, err := s.foodRepo.GetByIDWithMerchant(takeoutItem.FoodID)
		if err != nil {
			logs.Warn("获取食品信息失败: %v, 食品ID: %d", err, takeoutItem.FoodID)
			continue
		}

		// 获取变体价格
		var variantPrice float64 = food.Price
		if takeoutItem.VariantID > 0 {
			variant, err := s.variantRepo.GetByID(takeoutItem.VariantID)
			if err == nil {
				variantPrice = variant.Price
			}
		}

		// 获取商家优惠信息
		promotionRepo := repositories.NewTakeoutPromotionRepository()
		promotionInfo := "暂无促销活动"
		promotions := make([]dto.PromotionInfoDTO, 0)
		activePromotions, err := promotionRepo.GetActivePromotions(food.MerchantID)
		if err == nil && len(activePromotions) > 0 {
			// 构建促销信息文本
			promotionTexts := make([]string, 0)
			for _, promotion := range activePromotions {
				var promotionText, typeName string
				switch promotion.Type {
				case models.PromotionTypeFirstOrder:
					promotionText = "首单优惠"
					typeName = "首单优惠"
				case models.PromotionTypeProductDiscount:
					promotionText = "商品折扣"
					typeName = "商品折扣"
				case models.PromotionTypeCoupon:
					promotionText = "优惠券活动"
					typeName = "优惠券活动"
				case models.PromotionTypeFull:
					promotionText = "满减优惠"
					typeName = "满减活动"
				case models.PromotionTypeLimitedTime:
					promotionText = "限时特价"
					typeName = "限时特价"
				default:
					promotionText = promotion.Name
					typeName = "其他"
				}

				if promotion.Description != "" {
					promotionText += "(" + promotion.Description + ")"
				}
				promotionTexts = append(promotionTexts, promotionText)

				// 构建促销活动DTO
				promotionDTO := dto.PromotionInfoDTO{
					ID:          promotion.ID,
					Name:        promotion.Name,
					Description: promotion.Description,
					Type:        promotion.Type,
					TypeName:    typeName,
					Rules:       promotion.Rules,
					StartTime:   promotion.StartTime.Format("2006-01-02 15:04:05"),
					EndTime:     promotion.EndTime.Format("2006-01-02 15:04:05"),
				}
				promotions = append(promotions, promotionDTO)
			}
			if len(promotionTexts) > 0 {
				promotionInfo = promotionTexts[0] // 只显示第一个促销活动
				if len(promotionTexts) > 1 {
					promotionInfo += "等" + strconv.Itoa(len(promotionTexts)) + "项优惠"
				}
			}
		}

		// 构造购物车项DTO，优先使用food的信息以确保最新
		cartItemDTO := dto.TakeoutCartItemDTO{
			CartItemID:        cartItem.ID,
			MerchantID:        food.MerchantID,
			MerchantName:      food.MerchantName,
			MerchantLongitude: food.MerchantLongitude,
			MerchantLatitude:  food.MerchantLatitude,
			FoodID:            takeoutItem.FoodID,
			FoodName:          food.Name,
			FoodImage:         food.Image,
			VariantID:         takeoutItem.VariantID,
			VariantName:       takeoutItem.VariantName,
			Price:             variantPrice,
			OriginalPrice:     food.OriginalPrice,
			Quantity:          cartItem.Quantity,
			PackagingFee:      takeoutItem.PackagingFee,
			Subtotal:          variantPrice * float64(cartItem.Quantity),
			Remark:            takeoutItem.Remark,
			Selected:          cartItem.Selected,
			PromotionInfo:     promotionInfo,
			Promotions:        promotions,
		}

		// 处理套餐组合选择
		if food.IsCombination && takeoutItem.ComboSelectData != "" {
			comboSelections, err := s.parseComboSelectionsFromJson(takeoutItem.ComboSelectData)
			if err == nil {
				cartItemDTO.ComboSelections = comboSelections
			}
		}

		result = append(result, cartItemDTO)
	}

	logs.Info("ListCartItems完成构造DTO列表, 用户ID: %d, 最终返回条目数: %d", userID, len(result))
	// 打印最终返回的数据详情用于调试
	for i, item := range result {
		logs.Info("最终数据[%d]: CartItemID=%d, Quantity=%d", i, item.CartItemID, item.Quantity)
	}

	return result, nil
}

// CountCartItems 统计购物车商品数量
func (s *takeoutCartService) CountCartItems(userID int64) (int, error) {
	// 首先尝试从专门的计数缓存获取
	count, hit, err := s.cacheSvc.GetCartCount(userID)
	if err != nil {
		logs.Warn("获取购物车计数缓存出错: %v，将尝试其他方式", err)
	} else if hit {
		logs.Debug("购物车计数缓存命中, 用户ID: %d, 计数: %d", userID, count)
		return count, nil
	}

	// 计数缓存未命中，尝试从购物车列表缓存获取
	cartItems, hit, err := s.cacheSvc.GetCartItems(userID)
	if err != nil {
		logs.Warn("获取购物车列表缓存出错: %v，将从数据库获取", err)
	} else if hit {
		// 列表缓存命中，计算数量并存入计数缓存
		count := len(cartItems)
		logs.Debug("购物车列表缓存命中, 用户ID: %d, 计数: %d", userID, count)

		// 异步存储计数缓存
		go func() {
			if err := s.cacheSvc.SetCartCount(userID, count); err != nil {
				logs.Warn("存储购物车计数缓存失败: %v, 用户ID: %d", err, userID)
			}
		}()

		return count, nil
	}

	// 所有缓存都未命中，从数据库获取
	logs.Debug("购物车计数缓存未命中, 从数据库获取, 用户ID: %d", userID)
	takeoutItems, err := s.cartRepo.ListCartItemsByUserID(userID)
	if err != nil {
		logs.Error("获取外卖购物车项失败: %v, 用户ID: %d", err, userID)
		return 0, errors.New("查询购物车数量失败")
	}

	count = len(takeoutItems)

	// 异步存储计数缓存
	go func() {
		if err := s.cacheSvc.SetCartCount(userID, count); err != nil {
			logs.Warn("存储购物车计数缓存失败: %v, 用户ID: %d", err, userID)
		}
	}()

	return count, nil
}

// CountCartItemsWithDetails 统计购物车商品数量（详细信息）
func (s *takeoutCartService) CountCartItemsWithDetails(userID int64) (*dto.CartCountDetailsDTO, error) {
	// 首先尝试从专门的详细计数缓存获取
	details, hit, err := s.cacheSvc.GetCartCountDetails(userID)
	if err != nil {
		logs.Warn("获取购物车详细计数缓存出错: %v，将尝试其他方式", err)
	} else if hit {
		logs.Debug("购物车详细计数缓存命中, 用户ID: %d", userID)
		return details, nil
	}

	// 详细计数缓存未命中，尝试从购物车列表缓存获取
	cartItems, hit, err := s.cacheSvc.GetCartItems(userID)
	if err != nil {
		logs.Warn("获取购物车列表缓存出错: %v，将从数据库获取", err)
	} else if hit {
		// 列表缓存命中，计算详细信息并存入缓存
		details := s.calculateCartCountDetails(cartItems)
		logs.Debug("购物车列表缓存命中, 用户ID: %d", userID)

		// 异步存储详细计数缓存
		go func() {
			if err := s.cacheSvc.SetCartCountDetails(userID, details); err != nil {
				logs.Warn("存储购物车详细计数缓存失败: %v, 用户ID: %d", err, userID)
			}
		}()

		return details, nil
	}

	// 所有缓存都未命中，从数据库获取
	logs.Debug("购物车详细计数缓存未命中, 从数据库获取, 用户ID: %d", userID)
	cartItems, err = s.ListCartItems(userID)
	if err != nil {
		logs.Error("获取购物车项失败: %v, 用户ID: %d", err, userID)
		return nil, errors.New("查询购物车详情失败")
	}

	details = s.calculateCartCountDetails(cartItems)

	// 异步存储详细计数缓存
	go func() {
		if err := s.cacheSvc.SetCartCountDetails(userID, details); err != nil {
			logs.Warn("存储购物车详细计数缓存失败: %v, 用户ID: %d", err, userID)
		}
	}()

	return details, nil
}

// calculateCartCountDetails 计算购物车计数详情
func (s *takeoutCartService) calculateCartCountDetails(cartItems []dto.TakeoutCartItemDTO) *dto.CartCountDetailsDTO {
	details := &dto.CartCountDetailsDTO{}
	merchantSet := make(map[int64]bool)

	for _, item := range cartItems {
		// 统计总数
		details.TotalItems++
		details.TotalQuantity += item.Quantity
		details.TotalAmount += item.Subtotal

		// 统计商家数量
		merchantSet[item.MerchantID] = true

		// 根据选中状态分别统计
		if item.Selected {
			details.SelectedItems++
			details.SelectedQuantity += item.Quantity
			details.SelectedAmount += item.Subtotal
		} else {
			details.UnselectedItems++
			details.UnselectedQuantity += item.Quantity
		}
	}

	details.MerchantCount = len(merchantSet)
	return details
}

// GetCartSummary 获取购物车汇总信息
func (s *takeoutCartService) GetCartSummary(userID int64, cartItemIDs []int64) (*dto.TakeoutCartSummaryDTO, error) {
	// 记录获取购物车汇总信息的用户ID和购物车项IDs
	logs.Info("获取购物车汇总: userID=%d, cartItemIDs=%v", userID, cartItemIDs)

	// 获取指定购物车项详情
	items := make([]dto.TakeoutCartItemDTO, 0, len(cartItemIDs))

	for _, cartItemID := range cartItemIDs {
		item, err := s.GetCartItemDetails(userID, cartItemID)
		if err != nil {
			logs.Warn("获取购物车项详情失败: %v, 用户ID: %d, 购物车项ID: %d", err, userID, cartItemID)
			continue
		}

		items = append(items, *item)
	}

	// 计算总计
	var totalQuantity int
	var totalAmount, originalAmount, packagingFeeAmount float64

	for _, item := range items {
		totalQuantity += item.Quantity

		// 计算商品金额
		itemAmount := item.Price * float64(item.Quantity)
		totalAmount += itemAmount

		// 计算原价金额
		originalItemAmount := item.OriginalPrice * float64(item.Quantity)
		originalAmount += originalItemAmount

		// 计算包装费
		packagingFeeAmount += item.PackagingFee * float64(item.Quantity)

		// 计算套餐额外费用
		for _, combo := range item.ComboSelections {
			for _, option := range combo.SelectedOptions {
				totalAmount += option.ExtraPrice * float64(option.Quantity)
			}
		}
	}

	// 计算优惠金额
	discountAmount := originalAmount - totalAmount
	if discountAmount < 0 {
		discountAmount = 0
	}

	return &dto.TakeoutCartSummaryDTO{
		TotalItems:         len(items),
		TotalQuantity:      totalQuantity,
		TotalAmount:        totalAmount,
		OriginalAmount:     originalAmount,
		PackagingFeeAmount: packagingFeeAmount,
		DiscountAmount:     discountAmount,
		Items:              items,
	}, nil
}

// CheckoutCart 购物车结算
func (s *takeoutCartService) CheckoutCart(userID int64, cartItemIDs []int64, addressID int64) (*dto.TakeoutCartCheckoutDTO, error) {
	// 首先获取购物车汇总信息
	summary, err := s.GetCartSummary(userID, cartItemIDs)
	if err != nil {
		return nil, err
	}

	// 获取配送费用（这里应该根据地址计算，简化处理）
	var deliveryFee float64 = 5.0

	// 计算预计送达时间（这里简化处理）
	estimatedDeliveryTime := 30 // 30分钟

	// 构造结算DTO
	checkout := &dto.TakeoutCartCheckoutDTO{
		CartID:                0, // 这里应该创建一个新的购物车ID或使用用户的默认购物车ID
		TotalItems:            summary.TotalItems,
		TotalQuantity:         summary.TotalQuantity,
		TotalAmount:           summary.TotalAmount,
		OriginalAmount:        summary.OriginalAmount,
		PackagingFeeAmount:    summary.PackagingFeeAmount,
		DeliveryFee:           deliveryFee,
		DiscountAmount:        summary.DiscountAmount,
		PayAmount:             summary.TotalAmount + deliveryFee + summary.PackagingFeeAmount,
		Items:                 summary.Items,
		EatingStyle:           0, // 默认外卖配送
		EstimatedDeliveryTime: estimatedDeliveryTime,
		TablewareQuantity:     1, // 默认1套餐具
	}

	return checkout, nil
}

// convertCartToComboSelections 转换购物车套餐选择为标准套餐选择
func (s *takeoutCartService) convertCartToComboSelections(cartSelections []dto.CartComboSelectionRequest) []dto.ComboSelectionRequest {
	comboSelections := make([]dto.ComboSelectionRequest, len(cartSelections))

	for i, cartSelection := range cartSelections {
		// 提取选项ID
		optionIDs := make([]int64, len(cartSelection.SelectedOptions))
		for j, option := range cartSelection.SelectedOptions {
			optionIDs[j] = option.OptionID
		}

		// 创建ComboSelectionRequest
		comboSelections[i] = dto.ComboSelectionRequest{
			ItemID:    cartSelection.ComboItemID, // 添加 ComboItemID 字段
			OptionIDs: optionIDs,                 // 添加 SelectedOptions 字段
		}
	}

	return comboSelections
}

// 辅助方法：转换套餐选择为JSON格式
func (s *takeoutCartService) convertComboSelectionsToJson(selections []dto.ComboSelectionRequest) (string, error) {
	// 收集所有选项ID
	var optionIDs []int64
	optionQuantities := make(map[int64]int)
	for _, selection := range selections {
		// 针对ComboSelectionRequest结构体，处理OptionIDs字段
		for _, optionID := range selection.OptionIDs {
			optionIDs = append(optionIDs, optionID)
			// 默认选项数量为1，因为ComboSelectionRequest不包含数量
			optionQuantities[optionID] = 1
		}
	}

	// 批量获取选项信息
	options, err := s.comboRepo.BatchGetComboOptionsByIDs(optionIDs)
	if err != nil {
		return "", err
	}

	// 将选项信息映射为map
	optionMap := make(map[int64]*models.TakeoutComboOption)
	for _, option := range options {
		optionMap[option.ID] = option
	}

	// 将各个选项按组件分组
	itemOptionMap := make(map[int64][]int64)
	for _, option := range options {
		itemOptionMap[option.ComboItemID] = append(itemOptionMap[option.ComboItemID], option.ID)
	}

	// 查询所有相关的组件
	var itemIDs []int64
	for id := range itemOptionMap {
		itemIDs = append(itemIDs, id)
	}

	// 逐个获取套餐组件
	var comboItems []*models.TakeoutComboItem
	for _, itemID := range itemIDs {
		item, err := s.comboRepo.GetComboItemByID(itemID)
		if err != nil {
			logs.Warn("获取套餐组件失败: %v, 组件ID: %d", err, itemID)
			continue
		}
		comboItems = append(comboItems, item)
	}

	if len(comboItems) == 0 {
		return "", errors.New("无法获取套餐组件信息")
	}

	// 将组件信息映射为map
	itemMap := make(map[int64]*models.TakeoutComboItem)
	for _, item := range comboItems {
		itemMap[item.ID] = item
	}

	// 构造存储格式
	var comboSelections []models.ComboSelection
	for _, selection := range selections {
		item, ok := itemMap[selection.ItemID]
		if !ok {
			continue
		}

		var optionDetails []models.ComboOptionDetail
		for _, option := range selection.OptionIDs {
			opt, ok := optionMap[option]
			if !ok {
				continue
			}

			optionDetails = append(optionDetails, models.ComboOptionDetail{
				OptionID:   opt.ID,
				OptionName: opt.Name,
				ExtraPrice: opt.ExtraPrice,
				Quantity:   optionQuantities[opt.ID],
			})
		}

		comboSelections = append(comboSelections, models.ComboSelection{
			ComboItemID:     item.ID,
			ComboItemName:   item.Name,
			SelectedOptions: getOptionIDs(optionDetails),
			OptionDetails:   optionDetails,
		})
	}

	// 转换为JSON
	data, err := json.Marshal(comboSelections)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

// 辅助方法：从列表中提取ID
func getOptionIDs(details []models.ComboOptionDetail) []int64 {
	ids := make([]int64, 0, len(details))
	for _, detail := range details {
		ids = append(ids, detail.OptionID)
	}
	return ids
}

// 辅助方法：从JSON解析套餐选择
func (s *takeoutCartService) parseComboSelectionsFromJson(jsonData string) ([]dto.ComboSelectionResponse, error) {
	var comboSelections []models.ComboSelection
	err := json.Unmarshal([]byte(jsonData), &comboSelections)
	if err != nil {
		return nil, err
	}

	// 转换为响应DTO
	result := make([]dto.ComboSelectionResponse, 0, len(comboSelections))
	for _, selection := range comboSelections {
		optionSelections := make([]dto.ComboOptionSelectionResponse, 0, len(selection.OptionDetails))
		for _, option := range selection.OptionDetails {
			optionSelections = append(optionSelections, dto.ComboOptionSelectionResponse{
				OptionID:   option.OptionID,
				OptionName: option.OptionName,
				ExtraPrice: option.ExtraPrice,
				Quantity:   option.Quantity,
			})
		}

		result = append(result, dto.ComboSelectionResponse{
			ComboItemID:     selection.ComboItemID,
			ComboItemName:   selection.ComboItemName,
			SelectedOptions: optionSelections,
		})
	}

	return result, nil
}

// GetCartItems 获取用户购物车列表
func (s *takeoutCartService) GetCartItems(userID int64) ([]dto.TakeoutCartItemDTO, error) {
	logs.Info("开始获取购物车列表, 用户ID: %d", userID)

	// 尝试从缓存获取
	cartItems, hit, err := s.cacheSvc.GetCartItems(userID)
	if err != nil {
		logs.Warn("获取购物车缓存出错: %v，将从数据库获取", err)
	} else if hit {
		// 缓存命中
		logs.Info("购物车列表缓存命中, 用户ID: %d, 条目数: %d", userID, len(cartItems))
		// 打印缓存中的数据详情用于调试
		for i, item := range cartItems {
			logs.Info("缓存数据[%d]: CartItemID=%d, Quantity=%d", i, item.CartItemID, item.Quantity)
		}
		return cartItems, nil
	}

	logs.Info("购物车列表缓存未命中, 从数据库获取, 用户ID: %d", userID)
	// 缓存未命中，从数据库获取
	cartItems, err = s.ListCartItems(userID)
	if err != nil {
		return nil, err
	}

	logs.Info("从数据库获取购物车列表成功, 用户ID: %d, 条目数: %d", userID, len(cartItems))

	// 将结果存入缓存
	if len(cartItems) > 0 {
		if err := s.cacheSvc.SetCartItems(userID, cartItems); err != nil {
			logs.Warn("存储购物车缓存失败: %v", err)
		} else {
			logs.Info("购物车列表缓存存储成功, 用户ID: %d", userID)
		}
	}

	return cartItems, nil
}

// BatchRemoveFromCart 批量从购物车移除商品
func (s *takeoutCartService) BatchRemoveFromCart(userID int64, cartItemIDs []int64) error {
	if len(cartItemIDs) == 0 {
		return nil
	}

	logs.Info("批量删除购物车商品开始: 用户ID=%d, 购物车项ID=%v", userID, cartItemIDs)

	// 记录操作日志
	for _, cartItemID := range cartItemIDs {
		log := &models.TakeoutCartItemLog{
			UserID:       userID,
			CartItemID:   cartItemID,
			Action:       "batch_delete",
			Quantity:     0,
			IP:           "",
			ComboChanged: false,
		}
		_, _ = s.cartRepo.CreateCartItemLog(log)
	}

	// 批量删除外卖购物车项
	for _, cartItemID := range cartItemIDs {
		// 删除前记录日志，但不需要获取详细信息
		logs.Info("准备删除购物车项: 购物车项ID=%d", cartItemID)

		// 删除外卖购物车项
		err := s.cartRepo.DeleteCartItem(cartItemID)
		if err != nil {
			logs.Error("删除外卖购物车项失败: %v, 购物车项ID: %d", err, cartItemID)
			// 继续处理其他项
		}

		// 删除基础购物车项
		ctx := context.Background()
		_ = s.baseCartSvc.DeleteCartItem(ctx, &cartdto.DeleteCartItemRequest{
			ID:     cartItemID,
			UserID: userID,
		})
	}

	logs.Info("批量删除购物车商品完成: 用户ID=%d", userID)

	// 统一的缓存更新策略
	s.updateCartCacheAfterModification(userID, "batch_remove")

	return nil
}

// UpdateCart 整体更新购物车
func (s *takeoutCartService) UpdateCart(userID int64, req *dto.UpdateTakeoutCartRequest) error {
	logs.Info("[UpdateCart] 开始更新购物车, 用户ID: %d, 购物车项数量: %d", userID, len(req.CartItems))

	// 校验请求
	if req == nil || len(req.CartItems) == 0 {
		return errors.New("请求参数不能为空")
	}

	ctx := context.Background()

	// 验证购物车项是否属于该用户并更新
	for _, item := range req.CartItems {
		logs.Info("[UpdateCart] 处理购物车项, CartItemID: %d, Quantity: %d, Selected: %v", item.CartItemID, item.Quantity, item.Selected)

		// 验证购物车项是否存在且属于该用户
		cartItem, err := s.cartRepo.GetCartItemByCartItemID(item.CartItemID)
		if err != nil {
			logs.Error("获取购物车项失败: %v, 购物车项ID: %d", err, item.CartItemID)
			return errors.New("购物车项不存在")
		}

		// 构建更新请求
		updateReq := &cartdto.UpdateCartItemRequest{
			ID:     item.CartItemID,
			UserID: userID,
		}

		// 更新数量（如果提供了有效数量）
		if item.Quantity > 0 {
			updateReq.Quantity = item.Quantity
		}

		// 更新选中状态
		updateReq.Selected = &item.Selected

		// 调用基础购物车服务更新
		err = s.baseCartSvc.UpdateCartItem(ctx, updateReq)
		if err != nil {
			logs.Error("更新购物车项失败: %v, 购物车项ID: %d", err, item.CartItemID)
			return errors.New("更新购物车失败")
		}

		// 更新外卖购物车表的更新时间
		cartItem.UpdatedAt = time.Now()
		err = s.cartRepo.UpdateCartItem(cartItem)
		if err != nil {
			logs.Error("更新外卖购物车项时间戳失败: %v, 购物车项ID: %d", err, item.CartItemID)
			// 这里不返回错误，因为主要的更新已经成功了
		}
	}

	// 统一的缓存更新策略
	s.updateCartCacheAfterModification(userID, "update")

	logs.Info("[UpdateCart] 购物车更新完成, 用户ID: %d", userID)
	return nil
}

// SelectCartItem 选中购物车项
func (s *takeoutCartService) SelectCartItem(userID int64, req *dto.SelectCartItemRequest) error {
	// 验证请求
	if req == nil || len(req.CartItemIDs) == 0 {
		return errors.New("请求参数不能为空")
	}

	// 使用批量验证替代单个验证
	cartItems, err := s.cartRepo.ListCartItemsByCartIDs(req.CartItemIDs)
	if err != nil {
		logs.Error("批量获取购物车项失败: %v", err)
		return errors.New("获取购物车项失败")
	}

	// 验证购物车项数量是否一致（防止请求中包含不存在的购物车项）
	if len(cartItems) != len(req.CartItemIDs) {
		logs.Error("部分购物车项不存在，请求ID数量: %d, 实际找到: %d", len(req.CartItemIDs), len(cartItems))
		return errors.New("部分购物车项不存在")
	}

	// 使用批量更新操作替代单个更新
	ctx := context.Background()
	err = s.baseCartSvc.SelectCartItems(ctx, &cartdto.SelectCartItemsRequest{
		IDs:      req.CartItemIDs,
		UserID:   userID,
		Selected: req.Selected,
	})

	if err != nil {
		logs.Error("批量更新购物车项选中状态失败: %v", err)
		return errors.New("更新购物车失败")
	}

	// 统一的缓存更新策略
	s.updateCartCacheAfterModification(userID, "select")

	return nil
}

// ValidateCartItems 验证购物车商品有效性
func (s *takeoutCartService) ValidateCartItems(userID int64) ([]int64, error) {
	logs.Info("开始验证购物车商品有效性, 用户ID: %d", userID)

	// 获取购物车商品列表
	cartItems, err := s.GetCartItems(userID)
	if err != nil {
		logs.Error("获取购物车商品列表失败: %v", err)
		return nil, err
	}

	var invalidItems []int64

	// 验证每个商品
	for _, item := range cartItems {
		isValid := true

		// 检查商品是否存在且有效
		food, err := s.foodRepo.GetByID(item.FoodID)
		if err != nil {
			logs.Warn("获取商品信息失败, 商品ID: %d, 错误: %v", item.FoodID, err)
			isValid = false
		} else if food == nil {
			logs.Warn("商品不存在, 商品ID: %d", item.FoodID)
			isValid = false
		} else {
			// 检查商品状态
			if food.Status != 1 {
				logs.Info("商品已下架, 商品ID: %d, 状态: %d", item.FoodID, food.Status)
				isValid = false
			}

			// 检查是否售罄
			if food.SoldOut {
				logs.Info("商品已售罄, 商品ID: %d", item.FoodID)
				isValid = false
			}

			// 检查每日限量
			if food.DailyLimit > 0 && food.SoldCount >= food.DailyLimit {
				logs.Info("商品已达每日限量, 商品ID: %d, 限量: %d, 已售: %d",
					item.FoodID, food.DailyLimit, food.SoldCount)
				isValid = false
			}

			// 如果有规格变体，检查变体状态
			if item.VariantID > 0 {
				variant, err := s.variantRepo.GetByID(item.VariantID)
				if err != nil || variant == nil {
					logs.Warn("商品规格变体不存在, 变体ID: %d", item.VariantID)
					isValid = false
				} else {
					// 检查变体库存（假设变体有Stock字段，如果没有则跳过此检查）
					// 这里需要根据实际的变体模型来调整
					logs.Debug("检查变体状态, 变体ID: %d", item.VariantID)
				}
			}
		}

		// 如果商品无效，添加到无效列表
		if !isValid {
			invalidItems = append(invalidItems, item.CartItemID)
		}
	}

	logs.Info("购物车商品验证完成, 用户ID: %d, 总商品数: %d, 无效商品数: %d",
		userID, len(cartItems), len(invalidItems))

	// 确保返回的是空数组而不是nil
	if invalidItems == nil {
		invalidItems = []int64{}
	}

	return invalidItems, nil
}

// ClearInvalidCartItems 清除无效购物车商品
func (s *takeoutCartService) ClearInvalidCartItems(userID int64) error {
	logs.Info("开始清除无效购物车商品, 用户ID: %d", userID)

	// 获取无效商品列表
	invalidItems, err := s.ValidateCartItems(userID)
	if err != nil {
		logs.Error("验证购物车商品失败: %v", err)
		return err
	}

	if len(invalidItems) == 0 {
		logs.Info("没有无效商品需要清除, 用户ID: %d", userID)
		return nil
	}

	// 批量删除无效商品
	err = s.BatchRemoveFromCart(userID, invalidItems)
	if err != nil {
		logs.Error("批量删除无效商品失败: %v", err)
		return err
	}

	logs.Info("成功清除无效购物车商品, 用户ID: %d, 清除数量: %d", userID, len(invalidItems))

	return nil
}
