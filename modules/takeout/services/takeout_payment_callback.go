/*
 * takeout_payment_callback.go
 * 外卖订单支付回调处理器
 *
 * 本文件实现了外卖订单的支付回调处理，当支付成功后更新订单状态并创建跑腿订单
 */

package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/beego/beego/v2/core/logs"

	chatServices "o_mall_backend/modules/chat/services"
	orderServices "o_mall_backend/modules/order/services"
	"o_mall_backend/modules/payment/interfaces"
	paymentRepo "o_mall_backend/modules/payment/repositories"
	"o_mall_backend/modules/runner/dto"
	runnerServices "o_mall_backend/modules/runner/services"
	runnerImpl "o_mall_backend/modules/runner/services/impl"
	takeoutModels "o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"
)

// TakeoutPaymentCallback 外卖订单支付回调处理器
type TakeoutPaymentCallback struct {
	orderRepo    repositories.TakeoutOrderRepository
	runnerSvc    runnerServices.RunnerService
	paymentRepo  paymentRepo.PaymentRepository
	baseOrderSvc orderServices.OrderService
}

// NewTakeoutPaymentCallback 创建外卖订单支付回调处理器
func NewTakeoutPaymentCallback() interfaces.OrderPaymentCallback {
	return &TakeoutPaymentCallback{
		orderRepo:    repositories.NewTakeoutOrderRepository(),
		runnerSvc:    runnerImpl.NewRunnerService(),
		paymentRepo:  paymentRepo.NewPaymentRepository(),
		baseOrderSvc: orderServices.NewOrderService(),
	}
}

// HandlePaymentCallback 处理支付回调
func (c *TakeoutPaymentCallback) HandlePaymentCallback(ctx context.Context, orderNo string, paymentNo string, transactionID string, amount float64, payMethod int, rawData string) error {
	logs.Info("[外卖支付回调] ========== 开始处理支付回调 ===========")
	logs.Info("[外卖支付回调] 回调参数 - 订单号: %s, 支付流水号: %s, 交易ID: %s, 金额: %.2f, 支付方式: %d", orderNo, paymentNo, transactionID, amount, payMethod)
	logs.Info("[外卖支付回调] 原始数据: %s", rawData)

	// 根据支付流水号查询订单ID
	orderID, err := c.getOrderByPaymentNo(ctx, paymentNo)
	if err != nil {
		logs.Error("[外卖支付回调] 查询订单失败: %v, 支付流水号: %s", err, paymentNo)
		return err
	}

	logs.Info("[外卖支付回调] 找到订单 - 订单ID: %d", orderID)

	// 获取外卖订单扩展信息
	takeoutOrder, err := c.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		logs.Error("[外卖支付回调] 获取外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return err
	}

	// 检查订单是否已支付
	if takeoutOrder.DeliveryStatus >= 1 {
		logs.Info("[外卖支付回调] 订单已处理，跳过处理 - 订单ID: %d", orderID)
		return nil
	}

	// 更新外卖订单扩展信息的配送状态
	takeoutOrder.DeliveryStatus = 1 // 正在取餐
	now := time.Now()
	takeoutOrder.UpdatedAt = now

	err = c.orderRepo.UpdateOrderExtension(takeoutOrder)
	if err != nil {
		logs.Error("[外卖支付回调] 更新外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return err
	}

	logs.Info("[外卖支付回调] 订单状态更新成功 - 订单ID: %d, 配送状态: %d", orderID, takeoutOrder.DeliveryStatus)

	// 异步发送支付成功通知
	go func() {
		err := c.sendPaymentSuccessNotifications(ctx, orderID, orderNo, paymentNo, transactionID, amount)
		if err != nil {
			logs.Error("[外卖支付回调] 发送支付成功通知失败: %v, 订单ID: %d", err, orderID)
		}
	}()

	// 创建跑腿订单
	err = c.createRunnerOrder(ctx, orderID, takeoutOrder)
	if err != nil {
		logs.Error("[外卖支付回调] 创建跑腿订单失败: %v, 外卖订单ID: %d", err, orderID)
		// 不影响支付回调的成功，但记录错误
		return nil
	}

	logs.Info("[外卖支付回调] 支付回调处理完成 - 订单ID: %d", orderID)
	return nil
}

// getOrderByPaymentNo 根据支付流水号查询订单
func (c *TakeoutPaymentCallback) getOrderByPaymentNo(ctx context.Context, paymentNo string) (int64, error) {
	logs.Info("[外卖支付回调] 开始查询支付记录 - 支付流水号: %s", paymentNo)

	// 通过支付流水号查询支付记录
	payment, err := c.paymentRepo.GetPaymentByTransactionNo(paymentNo)
	if err != nil {
		logs.Error("[外卖支付回调] 查询支付记录失败: %v, 支付流水号: %s", err, paymentNo)
		return 0, err
	}

	if payment == nil {
		logs.Error("[外卖支付回调] 支付记录不存在, 支付流水号: %s", paymentNo)
		return 0, errors.New("支付记录不存在")
	}

	logs.Info("[外卖支付回调] 查询支付记录成功 - 支付ID: %d, 订单ID: %d, 状态: %s", payment.ID, payment.OrderID, payment.Status.String())
	return payment.OrderID, nil
}

// createRunnerOrder 创建跑腿订单
func (c *TakeoutPaymentCallback) createRunnerOrder(ctx context.Context, orderID int64, takeoutOrder *takeoutModels.TakeoutOrderExtension) error {
	logs.Info("[外卖支付回调] ========== 开始创建跑腿订单 ===========")
	logs.Info("[外卖支付回调] 外卖订单ID: %d, 订单号: %s, 配送状态: %d", orderID, takeoutOrder.OrderNo, takeoutOrder.DeliveryStatus)

	// 创建跑腿订单请求
	logs.Info("[外卖支付回调] 步骤1: 构造跑腿订单请求参数")
	request := &dto.CreateRunnerOrderRequest{
		OrderType:             0,                     // 0表示外卖订单
		DeliveryFee:           8.5,                   // 配送费用
		ServiceFee:            2.0,                   // 服务费用
		TipAmount:             0.0,                   // 小费金额
		Distance:              c.calculateDistance(), // 配送距离
		EstimateTime:          30,                    // 预计30分钟送达
		PickupAddress:         "商家地址",                // 暂时使用默认值
		PickupAddressDetail:   "商家详细地址",
		PickupLat:             39.9042, // 北京坐标
		PickupLng:             116.4074,
		PickupContact:         "商家联系人",
		PickupPhone:           "13800138000",
		DeliveryAddress:       "配送地址", // 暂时使用默认值
		DeliveryAddressDetail: "配送详细地址",
		DeliveryLat:           39.9042, // 北京坐标
		DeliveryLng:           116.4074,
		DeliveryContact:       "收货人",
		DeliveryPhone:         "13900139000",
		Goods:                 fmt.Sprintf("外卖订单 - 订单号: %s", takeoutOrder.OrderNo),
		GoodsWeight:           1.0,   // 商品重量
		GoodsValue:            100.0, // 商品价值
		Remark:                takeoutOrder.Remark,
		PayMethod:             1, // 支付方式
	}

	logs.Info("[外卖支付回调] 步骤2: 跑腿订单请求参数构造完成")
	logs.Info("[外卖支付回调] 请求参数详情 - 订单类型: %d, 配送费: %.2f, 服务费: %.2f, 距离: %.2f", request.OrderType, request.DeliveryFee, request.ServiceFee, request.Distance)
	logs.Info("[外卖支付回调] 取货地址: %s, 配送地址: %s", request.PickupAddress, request.DeliveryAddress)

	// 调用跑腿服务创建订单
	logs.Info("[外卖支付回调] 步骤3: 调用跑腿服务创建订单")
	if c.runnerSvc == nil {
		logs.Error("[外卖支付回调] 跑腿服务实例为空，无法创建跑腿订单")
		return errors.New("跑腿服务实例为空")
	}

	runnerOrderResponse, err := c.runnerSvc.CreateRunnerOrder(ctx, request, 0) // userID暂时设为0
	if err != nil {
		logs.Error("[外卖支付回调] 创建跑腿订单失败: %v, 外卖订单ID: %d", err, orderID)
		logs.Error("[外卖支付回调] 错误详情: %+v", err)
		return err
	}

	if runnerOrderResponse == nil {
		logs.Error("[外卖支付回调] 跑腿订单响应为空")
		return errors.New("跑腿订单响应为空")
	}

	logs.Info("[外卖支付回调] 跑腿订单创建成功 - 外卖订单ID: %d, 跑腿订单ID: %d", orderID, runnerOrderResponse.ID)
	logs.Info("[外卖支付回调] ========== 跑腿订单创建完成 ===========")
	return nil
}

// calculateDistance 计算配送距离
func (c *TakeoutPaymentCallback) calculateDistance() float64 {
	// 暂时返回固定距离，实际应该根据经纬度计算
	return 5.0 // 5公里
}

// sendPaymentSuccessNotifications 发送支付成功通知
func (c *TakeoutPaymentCallback) sendPaymentSuccessNotifications(ctx context.Context, orderID int64, orderNo string, paymentNo string, transactionID string, amount float64) error {
	logs.Info("[外卖支付回调] ========== 开始发送支付成功通知 ===========")
	logs.Info("[外卖支付回调] 订单ID: %d, 订单号: %s, 支付金额: %.2f", orderID, orderNo, amount)

	// 获取新的WebSocket通知服务
	container := chatServices.GetServiceContainer()
	userService := container.GetWebSocketUserService()
	merchantService := container.GetWebSocketMerchantService()

	if userService == nil || merchantService == nil {
		logs.Warn("[外卖支付回调] WebSocket通知服务未初始化，跳过通知发送")
		return nil
	}

	// 获取订单详细信息
	orderInfo, err := c.getOrderInfo(ctx, orderID)
	if err != nil {
		logs.Error("[外卖支付回调] 获取订单信息失败: %v, 订单ID: %d", err, orderID)
		return err
	}

	// 1. 通知用户支付成功
	logs.Info("[外卖支付回调] 发送用户支付成功通知 - 用户ID: %d", orderInfo.UserID)
	err = userService.SendOrderPaymentSuccessNotification(
		ctx,
		orderInfo.UserID,
		orderID,
		orderNo,
		amount,
	)
	if err != nil {
		logs.Error("[外卖支付回调] 发送用户支付成功通知失败: %v", err)
	} else {
		logs.Info("[外卖支付回调] 用户支付成功通知发送成功")
	}

	// 2. 通知商家有新的已支付订单
	logs.Info("[外卖支付回调] 发送商家新订单通知 - 商家ID: %d", orderInfo.MerchantID)

	// 构建客户信息
	customerInfo := map[string]interface{}{
		"user_id":          orderInfo.UserID,
		"user_name":        orderInfo.UserName,
		"user_phone":       orderInfo.UserPhone,
		"delivery_address": orderInfo.DeliveryAddress,
	}

	err = merchantService.SendNewOrderNotification(
		ctx,
		orderInfo.MerchantID,
		orderID,
		orderNo,
		amount,
		customerInfo,
	)
	if err != nil {
		logs.Error("[外卖支付回调] 发送商家新订单通知失败: %v", err)
	} else {
		logs.Info("[外卖支付回调] 商家新订单通知发送成功")
	}

	logs.Info("[外卖支付回调] 支付成功通知发送完成 - 订单ID: %d", orderID)
	return nil
}

// OrderInfo 订单信息结构体
type OrderInfo struct {
	UserID          int64  `json:"user_id"`
	MerchantID      int64  `json:"merchant_id"`
	UserName        string `json:"user_name"`
	MerchantName    string `json:"merchant_name"`
	ProductName     string `json:"product_name"`
	ProductCount    int    `json:"product_count"`
	DeliveryAddress string `json:"delivery_address"`
	UserPhone       string `json:"user_phone"`
}

// getOrderInfo 获取订单详细信息
func (c *TakeoutPaymentCallback) getOrderInfo(ctx context.Context, orderID int64) (*OrderInfo, error) {
	logs.Info("[外卖支付回调] 开始获取订单详细信息 - 订单ID: %d", orderID)

	// 获取基础订单信息
	orderService := orderServices.NewOrderService()
	order, err := orderService.GetOrder(ctx, orderID)
	if err != nil {
		logs.Error("[外卖支付回调] 获取基础订单信息失败: %v, 订单ID: %d", err, orderID)
		return nil, err
	}

	if order == nil {
		logs.Error("[外卖支付回调] 订单不存在: %d", orderID)
		return nil, errors.New("订单不存在")
	}

	// 获取外卖订单扩展信息
	takeoutOrder, err := c.orderRepo.GetOrderExtensionByOrderID(orderID)
	if err != nil {
		logs.Error("[外卖支付回调] 获取外卖订单扩展信息失败: %v, 订单ID: %d", err, orderID)
		return nil, err
	}

	// 获取商家ID和商品信息（从订单项中获取）
	var merchantID int64
	var productName string
	var productCount int

	if len(order.Items) > 0 {
		// 外卖订单通常只有一个商家，取第一个订单项的商家信息
		// 实际使用时可能需要从订单项的扩展字段或其他地方获取商家ID
		productName = order.Items[0].ProductName
		productCount = len(order.Items)

		// 这里需要根据实际的数据结构获取商家ID
		// 暂时使用默认值，实际使用时需要从外卖订单扩展信息中获取
		merchantID = 1 // 默认商家ID，实际需要从外卖订单扩展信息获取
	}

	// 构建订单信息
	orderInfo := &OrderInfo{
		UserID:          order.UserID,
		MerchantID:      merchantID,
		UserName:        "用户",          // 实际使用时需要从用户服务获取
		MerchantName:    "商家",          // 实际使用时需要从商家服务获取
		ProductName:     productName,   // 从订单项获取
		ProductCount:    productCount,  // 从订单项统计
		DeliveryAddress: "配送地址",        // 实际使用时需要从地址服务获取
		UserPhone:       "13800138000", // 实际使用时需要从用户服务获取
	}

	// 如果有外卖订单扩展信息，使用扩展信息中的数据
	if takeoutOrder != nil {
		// 这里可以从扩展信息中获取更详细的信息
		logs.Info("[外卖支付回调] 获取到外卖订单扩展信息 - 订单号: %s", takeoutOrder.OrderNo)
	}

	logs.Info("[外卖支付回调] 订单详细信息获取成功 - 订单ID: %d, 用户ID: %d, 商家ID: %d",
		orderID, orderInfo.UserID, orderInfo.MerchantID)

	return orderInfo, nil
}
