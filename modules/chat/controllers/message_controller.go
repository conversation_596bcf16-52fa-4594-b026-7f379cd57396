/**
 * message_controller.go
 * 聊天消息控制器
 *
 * 该文件实现了处理聊天消息相关的HTTP请求，包括发送消息、获取消息列表等功能
 */

package controllers

import (
	"context"
	"fmt"
	"path"
	"strconv"
	"time"

	"github.com/beego/beego/v2/core/logs"
	"github.com/google/uuid"

	"o_mall_backend/common/result"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
	"o_mall_backend/modules/chat/services"
	"o_mall_backend/utils/storage"
)

// MessageController 处理聊天消息相关的API请求
type MessageController struct {
	BaseController
	chatService     services.ChatService
	wsCommonService services.WebSocketCommonService
}

// Prepare 重写BaseController的Prepare方法，确保每次请求都获取正确的服务实例
func (c *MessageController) Prepare() {
	// 先调用基类的Prepare方法
	c.BaseController.Prepare()

	// 从服务容器获取服务实例
	container := services.GetServiceContainer()
	if container != nil {
		// 获取ChatService
		c.chatService = container.GetChatService()
		if c.chatService == nil {
			logs.Error("[MessageController.Prepare] 从服务容器获取chatService失败")
		} else {
			logs.Debug("[MessageController.Prepare] 成功获取chatService实例")
		}

		// 获取WebSocketCommonService
		c.wsCommonService = container.GetWebSocketCommonService()
		if c.wsCommonService == nil {
			logs.Error("[MessageController.Prepare] 从服务容器获取wsCommonService失败")
		} else {
			logs.Debug("[MessageController.Prepare] 成功获取wsCommonService实例")
		}
	}
}

// NewMessageController 创建消息控制器实例
func NewMessageController(chatService services.ChatService, wsCommonService services.WebSocketCommonService) *MessageController {
	return &MessageController{
		chatService:     chatService,
		wsCommonService: wsCommonService,
	}
}

// GetMessages 获取会话中的消息列表
// @Title 获取消息列表
// @Description 获取指定会话的消息列表
// @Param session_id path int true "会话ID"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Param order query string false "排序方式，asc:升序，desc:倒序，默认desc"
// @Success 200 {object} result.Response{data=dto.MessageListResponse} "消息列表"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:session_id/messages [get]
func (c *MessageController) GetMessages() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	_, ok = c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":session_id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 解析分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)
	order := c.GetString("order")
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}
	// 验证排序参数，默认为desc（倒序）
	if order != "asc" && order != "desc" {
		order = "desc"
	}

	// 验证用户是否有权限访问该会话
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话信息失败", err.Error())
		return
	}

	// 验证用户权限
	userRole, _ := c.GetCurrentUserRole()
	if !c.hasSessionPermission(session, userID, userRole) {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
		return
	}

	// 获取消息列表
	messages, total, err := c.chatService.GetSessionMessages(
		context.Background(),
		sessionID,
		page,
		pageSize,
		order,
	)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取消息列表失败", err.Error())
		return
	}

	// 添加调试日志
	for i, msg := range messages {
		logs.Debug("[GetMessages] 返回消息 #%d: ID=%d, Type=%s, Content=%s", i, msg.ID, msg.Type, msg.Content)
	}

	// 返回消息列表
	c.responseSuccess(map[string]interface{}{
		"list":       messages,
		"total":      total,
		"page":       page,
		"page_size":  pageSize,
		"page_count": (total + int64(pageSize) - 1) / int64(pageSize),
	})
}

// SendTextMessage 发送文本消息
// @Title 发送文本消息
// @Description 向指定会话发送文本消息
// @Param session_id path int true "会话ID"
// @Param body body dto.SendTextMessageRequest true "消息内容"
// @Success 200 {object} result.Response{data=models.ChatMessage} "发送的消息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:session_id/messages/text [post]
func (c *MessageController) SendTextMessage() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	_, ok = c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":session_id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 解析请求参数
	var req dto.SendTextMessageRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", err.Error())
		return
	}

	// 验证用户是否有权限访问该会话
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话信息失败", err.Error())
		return
	}

	// 验证用户权限
	userRole, _ := c.GetCurrentUserRole()
	if !c.hasSessionPermission(session, userID, userRole) {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
		return
	}

	// 发送文本消息
	message, err := c.chatService.SendTextMessage(
		context.Background(),
		sessionID,
		userID,
		userRole, // 使用当前用户的实际角色
		req.Content,
	)
	if err != nil {
		c.responseError(result.CodeInternalError, "发送消息失败", err.Error())
		return
	}

	// 通过WebSocket服务广播消息
	if c.wsCommonService != nil {
		err = c.wsCommonService.BroadcastChatMessage(context.Background(), message)
		if err != nil {
			logs.Error("[MessageController.SendTextMessage] WebSocket广播消息失败: %v", err)
			// 不阻塞API响应，只记录错误
		}
	}

	// 将ChatMessage转换为MessageDTO以包含发送者信息
	messageDTO := c.convertChatMessageToDTO(message)

	// 返回消息信息
	c.responseSuccess(map[string]interface{}{
		"message": "发送成功",
		"data":    messageDTO,
	})
}

// SendMediaMessage 发送媒体消息（图片、文件、语音、视频）
// @Title 发送媒体消息
// @Description 向指定会话发送媒体消息（图片、文件、语音、视频）
// @Param session_id path int true "会话ID"
// @Param type query string true "媒体类型：image(图片)、file(文件)、voice(语音)、video(视频)"
// @Param file formData file true "媒体文件"
// @Success 200 {object} result.Response{data=models.ChatMessage} "发送的消息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:session_id/messages/media [post]
func (c *MessageController) SendMediaMessage() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	_, ok = c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":session_id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 获取媒体类型
	mediaType := c.GetString("type")
	if mediaType != "image" && mediaType != "file" && mediaType != "voice" && mediaType != "video" {
		c.responseError(result.CodeInvalidParams, "参数错误", "无效的媒体类型")
		return
	}

	// 验证用户是否有权限访问该会话
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话信息失败", err.Error())
		return
	}

	// 验证用户权限
	userRole, _ := c.GetCurrentUserRole()
	if !c.hasSessionPermission(session, userID, userRole) {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
		return
	}

	// 获取上传的文件
	file, header, err := c.GetFile("file")
	if err != nil {
		c.responseError(result.CodeInvalidParams, "获取文件失败", err.Error())
		return
	}
	defer file.Close()

	// 生成唯一文件名
	ext := path.Ext(header.Filename)
	fileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// 确定存储目录
	fileDir := fmt.Sprintf("chat/%s/%s", mediaType, time.Now().Format("2006/01/02"))

	// 保存文件到存储
	filePath, err := storage.Save(file, fileName, fileDir)
	if err != nil {
		logs.Error("[MessageController] 保存媒体文件失败: %v", err)
		c.responseError(result.CodeInternalError, "保存媒体文件失败", err.Error())
		return
	}

	// 发送媒体消息
	message, err := c.chatService.SendMediaMessage(
		context.Background(),
		sessionID,
		userID,
		userRole, // 使用当前用户的实际角色
		mediaType,
		filePath,
	)
	if err != nil {
		c.responseError(result.CodeInternalError, "发送媒体消息失败", err.Error())
		return
	}

	// 通过WebSocket服务广播消息
	if c.wsCommonService != nil {
		err = c.wsCommonService.BroadcastChatMessage(context.Background(), message)
		if err != nil {
			logs.Error("[MessageController.SendMediaMessage] WebSocket广播消息失败: %v", err)
			// 不阻塞API响应，只记录错误
		}
	}

	// 将ChatMessage转换为MessageDTO以包含发送者信息
	messageDTO := c.convertChatMessageToDTO(message)

	// 返回消息信息
	c.responseSuccess(map[string]interface{}{
		"message": "发送成功",
		"data":    messageDTO,
	})
}

// SendMediaURLMessage 发送媒体URL消息（处理已上传的媒体文件URL）
// @Title 发送媒体URL消息
// @Description 向指定会话发送已上传的媒体文件URL消息
// @Param session_id path int true "会话ID"
// @Param body body dto.SendMediaURLRequest true "媒体URL消息内容"
// @Success 200 {object} result.Response{data=models.ChatMessage} "发送的消息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /sessions/:session_id/messages/media-url [post]
func (c *MessageController) SendMediaURLMessage() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	_, ok = c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取会话ID
	sessionIDStr := c.Ctx.Input.Param(":session_id")
	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.responseError(result.CodeInvalidParams, "参数错误", "会话ID无效")
		return
	}

	// 解析请求参数
	var req dto.SendMediaURLRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 验证媒体类型
	if req.MessageType != "image" && req.MessageType != "file" && req.MessageType != "voice" && req.MessageType != "video" {
		c.responseError(result.CodeInvalidParams, "参数错误", "无效的媒体类型")
		return
	}

	// 验证URL不为空
	if req.Content == "" {
		c.responseError(result.CodeInvalidParams, "参数错误", "媒体URL不能为空")
		return
	}

	// 验证用户是否有权限访问该会话
	session, err := c.chatService.GetSession(context.Background(), sessionID)
	if err != nil {
		c.responseError(result.CodeInternalError, "获取会话信息失败", err.Error())
		return
	}

	// 验证用户权限
	userRole, _ := c.GetCurrentUserRole()
	if !c.hasSessionPermission(session, userID, userRole) {
		c.responseError(result.CodeForbidden, "权限错误", "无权访问该会话")
		return
	}

	// 发送媒体URL消息
	message, err := c.chatService.SendMediaURLMessage(
		context.Background(),
		sessionID,
		userID,
		userRole, // 使用当前用户的实际角色
		req.MessageType,
		req.Content,
		req.FileName,
		req.FileSize,
		req.FileType,
		req.FileExt,
	)
	if err != nil {
		c.responseError(result.CodeInternalError, "发送媒体URL消息失败", err.Error())
		return
	}

	// 通过WebSocket服务广播消息
	if c.wsCommonService != nil {
		err = c.wsCommonService.BroadcastChatMessage(context.Background(), message)
		if err != nil {
			logs.Error("[MessageController.SendMediaURLMessage] WebSocket广播消息失败: %v", err)
			// 不阻塞API响应，只记录错误
		}
	}

	// 将ChatMessage转换为MessageDTO以包含发送者信息
	messageDTO := c.convertChatMessageToDTO(message)

	// 返回消息信息
	c.responseSuccess(map[string]interface{}{
		"message": "发送成功",
		"data":    messageDTO,
	})
}

// convertChatMessageToDTO 将ChatMessage转换为包含发送者信息的MessageDTO
func (c *MessageController) convertChatMessageToDTO(chatMessage *models.ChatMessage) *dto.MessageDTO {
	// 创建MessageDTO
	messageDTO := &dto.MessageDTO{
		ID:         chatMessage.ID,
		SessionID:  chatMessage.SessionID,
		SenderID:   chatMessage.SenderID,
		SenderType: chatMessage.SenderType,
		Content:    chatMessage.Content,
		Type:       chatMessage.Type,
		ResourceID: chatMessage.ResourceID,
		FileName:   chatMessage.FileName,
		FileSize:   chatMessage.FileSize,
		FileType:   chatMessage.FileType,
		FileExt:    chatMessage.FileExt,
		Status:     chatMessage.Status,
		CreatedAt:  chatMessage.CreatedAt,
	}

	// 处理媒体消息的URL
	if chatMessage.Type == "image" || chatMessage.Type == "file" || chatMessage.Type == "voice" || chatMessage.Type == "video" {
		if chatMessage.ResourceID != "" {
			// 如果有ResourceID（上传文件的媒体消息），将文件路径转换为完整URL并放入Content字段
			fileURL := storage.GetFileURL(chatMessage.ResourceID)
			if fileURL != "" {
				messageDTO.Content = fileURL
				messageDTO.ResourceID = fileURL // 保持ResourceID也是URL，用于兼容性
				logs.Debug("[convertChatMessageToDTO] 媒体消息URL转换: %s -> %s", chatMessage.ResourceID, fileURL)
			} else {
				logs.Warn("[convertChatMessageToDTO] 无法获取文件URL，文件路径: %s", chatMessage.ResourceID)
			}
		} else if chatMessage.Content != "" {
			// 如果没有ResourceID但有Content（URL媒体消息），Content已经是URL，保持不变
			messageDTO.ResourceID = chatMessage.Content // 保持ResourceID也是URL，用于兼容性
			logs.Debug("[convertChatMessageToDTO] URL媒体消息，Content已是URL: %s", chatMessage.Content)
		}
	}

	// 填充发送者信息（头像等）
	if c.chatService != nil {
		c.chatService.FillSenderInfo(context.Background(), messageDTO)
	} else {
		logs.Error("[MessageController] 聊天服务为空，无法填充发送者信息")
		// 设置默认值
		messageDTO.SenderName = "未知发送者"
		messageDTO.SenderAvatar = ""
	}

	return messageDTO
}

// 注意：不再需要responseError方法，因为已经继承自BaseController

// SendMessage 发送消息（统一入口点）
// @Title 发送消息
// @Description 统一的消息发送入口，根据消息类型分发到不同处理方法
// @Param body body dto.SendMessageRequest true "消息内容"
// @Success 200 {object} result.Response{data=models.ChatMessage} "发送的消息"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /messages [post]
func (c *MessageController) SendMessage() {
	// 获取当前用户信息
	_, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	_, ok = c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 解析请求参数
	var req dto.SendMessageRequest
	if err := c.ParseRequest(&req); err != nil {
		c.responseError(result.CodeInvalidParams, "参数解析错误", err.Error())
		return
	}

	// 根据消息类型分发到不同处理逻辑
	switch req.Type {
	case "text":
		// 设置目标会话并转发到文本消息处理方法
		c.Ctx.Input.SetParam(":session_id", strconv.FormatInt(req.SessionID, 10))
		// 在Beego中添加文本内容到请求中
		c.Data["json"] = map[string]interface{}{
			"content": req.Content,
		}
		c.SendTextMessage()
		return
	case "image", "file", "voice":
		// 设置参数并转发到媒体消息处理
		c.Ctx.Input.SetParam(":session_id", strconv.FormatInt(req.SessionID, 10))
		// 在Beego中设置查询参数，使用c.Ctx.Request.URL.Query()而不是SetQuery
		values := c.Ctx.Request.URL.Query()
		values.Set("type", req.Type)
		c.Ctx.Request.URL.RawQuery = values.Encode()
		c.SendMediaMessage()
		return
	default:
		c.responseError(result.CodeInvalidParams, "参数错误", "不支持的消息类型")
		return
	}
}

// GetMessageList 获取会话消息列表
// @Title 获取消息列表
// @Description 获取指定会话的消息列表
// @Param session_id path int true "会话ID"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Param order query string false "排序方式，asc:升序，desc:倒序，默认desc"
// @Success 200 {object} result.Response{data=dto.MessageListResponse} "消息列表"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 403 {object} result.Response "权限错误"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /messages/:session_id [get]
func (c *MessageController) GetMessageList() {
	// 直接调用GetMessages，保持代码一致性
	c.GetMessages()
}

// GetNotificationMessages 获取用户的通知消息
// @Title 获取通知消息
// @Description 获取当前用户的系统通知消息列表
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Param order query string false "排序方式，asc或desc，默认desc"
// @Success 200 {object} result.Response "成功"
// @Failure 400 {object} result.Response "请求参数错误"
// @Failure 401 {object} result.Response "未授权"
// @Failure 500 {object} result.Response "服务器内部错误"
// @router /notifications [get]
func (c *MessageController) GetNotificationMessages() {
	// 获取当前用户信息
	userID, ok := c.GetCurrentUserID()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户ID")
		return
	}

	userRole, ok := c.GetCurrentUserRole()
	if !ok {
		c.responseError(result.CodeUnauthorized, "未授权", "无法获取用户角色")
		return
	}

	// 获取分页参数
	page, _ := c.GetInt("page", 1)
	pageSize, _ := c.GetInt("page_size", 20)
	order := c.GetString("order")
	if order == "" {
		order = "desc"
	}

	// 验证分页参数
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	logs.Info("[GetNotificationMessages] 获取通知消息: userID=%d, userRole=%s, page=%d, pageSize=%d",
		userID, userRole, page, pageSize)

	// 获取或创建系统通知会话
	sessionID, err := c.getOrCreateSystemNotificationSession(context.Background(), userID, userRole)
	if err != nil {
		logs.Error("[GetNotificationMessages] 获取系统通知会话失败: %v", err)
		c.responseError(result.CodeInternalError, "获取通知消息失败", err.Error())
		return
	}

	// 获取会话中的通知消息
	messages, total, err := c.chatService.GetSessionMessages(context.Background(), sessionID, page, pageSize, order)
	if err != nil {
		logs.Error("[GetNotificationMessages] 获取通知消息失败: %v", err)
		c.responseError(result.CodeInternalError, "获取通知消息失败", err.Error())
		return
	}

	// 过滤出通知类型的消息
	var notificationMessages []*dto.MessageDTO
	for _, msg := range messages {
		if msg.Type == models.MessageTypeNotification {
			notificationMessages = append(notificationMessages, msg)
		}
	}

	logs.Info("[GetNotificationMessages] 获取通知消息成功: userID=%d, 总数=%d, 通知消息数=%d",
		userID, total, len(notificationMessages))

	// 返回通知消息列表
	c.responseSuccess(map[string]interface{}{
		"list":       notificationMessages,
		"total":      len(notificationMessages),
		"page":       page,
		"page_size":  pageSize,
		"session_id": sessionID,
	})
}

// getOrCreateSystemNotificationSession 获取或创建系统通知会话
func (c *MessageController) getOrCreateSystemNotificationSession(ctx context.Context, userID int64, userType string) (int64, error) {
	// 使用FindOrCreateSession方法获取或创建系统通知会话
	session, err := c.chatService.FindOrCreateSession(ctx, 0, "system", userID, userType)
	if err != nil {
		return 0, err
	}

	return session.ID, nil
}

// Options 处理CORS预检请求
// @Title 处理CORS预检请求
// @Description 处理CORS预检请求，返回允许的HTTP方法和头信息
// @Router /api/v1/chat/sessions/{session_id}/messages [options]
// @Router /api/v1/chat/sessions/{session_id}/messages/text [options]
// @Router /api/v1/chat/sessions/{session_id}/messages/media [options]
// @Router /api/v1/chat/sessions/{session_id}/messages/media-url [options]
// @Success 200 {string} string "OK"
func (c *MessageController) Options() {
	// OPTIONS请求已经在CORS中间件中处理，这里只需要返回200状态码
	c.Ctx.Output.SetStatus(200)
}

// hasSessionPermission 检查用户是否有权限访问会话
func (c *MessageController) hasSessionPermission(session *models.ChatSession, userID int64, userRole string) bool {
	// 群聊权限验证
	if session.Type == models.SessionTypeGroup {
		// 检查用户是否为群成员
		groupMemberService := &services.GroupMemberService{}
		_, err := groupMemberService.GetMemberByUserID(session.ReceiverID, userID, userRole)
		return err == nil
	}

	// 一对一聊天权限验证
	// 检查是否为会话的参与者（创建者或接收者）
	isCreator := session.CreatorID == userID && session.CreatorType == userRole
	isReceiver := session.ReceiverID == userID && session.ReceiverType == userRole

	return isCreator || isReceiver
}
