/**
 * init.go
 * 聊天模块初始化
 *
 * 该文件实现了聊天模块的初始化，包括依赖注入、组件配置和服务注册
 */

package chat

import (
	"github.com/beego/beego/v2/core/logs"

	"o_mall_backend/modules/chat/routers"
	"o_mall_backend/modules/chat/services"
)

// Init 初始化聊天模块
func Init() {
	// 注意：模型已在各模型文件的init()函数中注册，这里不需要重复注册

	// 注册路由
	routers.Init()

	// 确保WebSocket管理器在应用启动时就初始化
	// 这对于其他模块发送WebSocket通知至关重要
	initWebSocketServices()

	// 日志输出
	logs.Info("聊天模块初始化完成")
}

// initWebSocketServices 初始化WebSocket相关服务
func initWebSocketServices() {
	logs.Info("[聊天模块] 开始初始化WebSocket服务...")

	// 获取服务容器
	container := services.GetServiceContainer()

	// 检查WebSocket管理器是否已初始化
	wsManager := container.GetWebSocketManager()
	if wsManager == nil {
		logs.Warn("[聊天模块] WebSocket管理器未初始化，正在创建...")

		// 创建WebSocket管理器
		wsManager = services.NewWebSocketManager()
		container.SetWebSocketManager(wsManager)

		logs.Info("[聊天模块] WebSocket管理器已创建并设置到服务容器")
	} else {
		logs.Info("[聊天模块] WebSocket管理器已存在")
	}

	// 确保异步消息服务已启动
	asyncService := container.GetAsyncMessageService()
	if asyncService == nil {
		logs.Info("[聊天模块] 创建并启动异步消息服务...")
		asyncService = services.NewAsyncMessageService()
		if err := asyncService.Start(); err != nil {
			logs.Error("[聊天模块] 启动异步消息服务失败: %v", err)
		} else {
			logs.Info("[聊天模块] 异步消息服务已启动")
		}
		container.SetAsyncMessageService(asyncService)
	}

	logs.Info("[聊天模块] WebSocket服务初始化完成")
}
