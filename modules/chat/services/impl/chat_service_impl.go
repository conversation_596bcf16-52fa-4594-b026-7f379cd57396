/**
 * chat_service_impl.go
 * 聊天服务实现
 *
 * 该文件实现了聊天模块的业务逻辑，处理聊天会话和消息的管理及传递
 */

package impl

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	adminServices "o_mall_backend/modules/admin/services"
	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
	"o_mall_backend/modules/chat/repositories"
	"o_mall_backend/modules/chat/services"
	merchantServices "o_mall_backend/modules/merchant/services"
	userServices "o_mall_backend/modules/user/services"
	"o_mall_backend/utils/storage"
)

// ChatServiceImpl 聊天服务实现类
type ChatServiceImpl struct {
	chatRepo            repositories.ChatRepository
	wsManager           services.WebSocketManager
	userService         userServices.UserService
	merchantService     merchantServices.MerchantService
	adminService        adminServices.AdminService
	groupService        *services.GroupService
	onlineStatusService services.OnlineStatusService
}

// NewChatService 创建聊天服务实例
func NewChatService(chatRepo repositories.ChatRepository, wsManager services.WebSocketManager, referralConfigService userServices.ReferralConfigService) services.ChatService {
	return &ChatServiceImpl{
		chatRepo:            chatRepo,
		wsManager:           wsManager,
		userService:         userServices.NewUserService(referralConfigService),
		merchantService:     merchantServices.NewMerchantService(),
		adminService:        adminServices.NewAdminService(),
		groupService:        services.NewGroupService(),
		onlineStatusService: services.NewOnlineStatusService(),
	}
}

// CreateSession 创建聊天会话
func (s *ChatServiceImpl) CreateSession(
	ctx context.Context,
	creatorID int64,
	creatorType string,
	receiverID int64,
	receiverType string,
) (*models.ChatSession, error) {
	// 验证接收者是否存在
	if err := s.validateReceiver(ctx, receiverID, receiverType); err != nil {
		logs.Error("[ChatService] 验证接收者失败: %v", err)
		return nil, fmt.Errorf("验证接收者失败: %v", err)
	}

	// 首先检查是否已存在会话
	existingSession, err := s.chatRepo.FindSessionByParticipants(ctx, creatorID, creatorType, receiverID, receiverType)
	if err != nil {
		logs.Error("[ChatService] 检查会话是否存在失败: %v", err)
		return nil, fmt.Errorf("检查会话是否存在失败: %v", err)
	}

	// 如果已存在会话，直接返回
	if existingSession != nil {
		return existingSession, nil
	}

	// 确定会话类型
	sessionType := s.determineSessionType(creatorType, receiverType)

	// 创建新会话
	session := &models.ChatSession{
		Type:         sessionType,
		CreatorID:    creatorID,
		CreatorType:  creatorType,
		ReceiverID:   receiverID,
		ReceiverType: receiverType,
		Status:       0, // 正常状态
	}

	// 保存会话
	id, err := s.chatRepo.CreateSession(ctx, session)
	if err != nil {
		logs.Error("[ChatService] 创建会话失败: %v", err)
		return nil, fmt.Errorf("创建会话失败: %v", err)
	}

	// 查询完整会话信息
	session, err = s.chatRepo.GetSessionByID(ctx, id)
	if err != nil {
		logs.Error("[ChatService] 获取创建的会话失败: %v", err)
		return nil, fmt.Errorf("获取创建的会话失败: %v", err)
	}

	return session, nil
}

// validateReceiver 验证接收者是否存在
func (s *ChatServiceImpl) validateReceiver(ctx context.Context, receiverID int64, receiverType string) error {
	switch receiverType {
	case "user":
		// 验证用户是否存在
		if _, err := s.userService.GetUserByID(ctx, receiverID); err != nil {
			return fmt.Errorf("用户不存在: %v", err)
		}
	case "merchant":
		// 验证商家是否存在
		if _, err := s.merchantService.GetMerchantByID(ctx, receiverID); err != nil {
			return fmt.Errorf("商家不存在: %v", err)
		}
	case "admin":
		// 验证管理员是否存在（这里可以根据实际情况添加验证逻辑）
		// 暂时跳过管理员验证
	default:
		return fmt.Errorf("不支持的接收者类型: %s", receiverType)
	}
	return nil
}

// determineSessionType 根据创建者和接收者类型确定会话类型
func (s *ChatServiceImpl) determineSessionType(creatorType, receiverType string) string {
	// 如果是相同类型的用户，默认为用户对用户
	if creatorType == receiverType {
		return models.SessionTypeUserToUser
	}

	// 根据不同的用户类型组合确定会话类型
	switch {
	case (creatorType == "user" && receiverType == "merchant") || (creatorType == "merchant" && receiverType == "user"):
		return models.SessionTypeUserToMerchant
	case (creatorType == "user" && receiverType == "admin") || (creatorType == "admin" && receiverType == "user"):
		return models.SessionTypeUserToAdmin
	case (creatorType == "admin" && receiverType == "merchant") || (creatorType == "merchant" && receiverType == "admin"):
		return models.SessionTypeAdminToMerchant
	default:
		// 默认返回用户对用户类型
		return models.SessionTypeUserToUser
	}
}

// GetSession 获取会话信息
func (s *ChatServiceImpl) GetSession(ctx context.Context, sessionID int64) (*models.ChatSession, error) {
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		logs.Error("[ChatService] 获取会话失败: %v", err)
		return nil, fmt.Errorf("获取会话失败: %v", err)
	}
	if session == nil {
		return nil, fmt.Errorf("会话不存在")
	}
	return session, nil
}

// FindOrCreateSession 查找或创建会话
func (s *ChatServiceImpl) FindOrCreateSession(
	ctx context.Context,
	creatorID int64,
	creatorType string,
	receiverID int64,
	receiverType string,
) (*models.ChatSession, error) {
	return s.CreateSession(ctx, creatorID, creatorType, receiverID, receiverType)
}

// GetUserSessions 获取用户的会话列表
func (s *ChatServiceImpl) GetUserSessions(
	ctx context.Context,
	userID int64,
	userType string,
	page, pageSize int,
) ([]*dto.SessionDTO, int64, error) {
	// 获取会话列表
	sessions, total, err := s.chatRepo.ListSessionsByUser(ctx, userID, userType, page, pageSize)
	if err != nil {
		logs.Error("[ChatService] 获取用户会话列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取用户会话列表失败: %v", err)
	}

	// 转换为DTO（使用公共方法）
	sessionDTOs := make([]*dto.SessionDTO, 0, len(sessions))
	for _, session := range sessions {
		sessionDTO := s.convertSessionToDTO(ctx, session, userID, userType)
		if sessionDTO != nil {
			sessionDTOs = append(sessionDTOs, sessionDTO)
		}
	}

	return sessionDTOs, total, nil
}

// GetUserSessionsByCategory 根据消息分类获取用户的会话列表
func (s *ChatServiceImpl) GetUserSessionsByCategory(
	ctx context.Context,
	userID int64,
	userType string,
	category string,
	page, pageSize int,
) ([]*dto.SessionDTO, int64, error) {
	// 如果没有指定分类，返回所有会话
	if category == "" {
		return s.GetUserSessions(ctx, userID, userType, page, pageSize)
	}

	// 根据分类筛选会话类型
	var sessionTypes []string
	switch category {
	case "chat":
		// 聊天消息：用户对用户的会话和群聊会话
		sessionTypes = []string{"user_to_user", "group"}
	case "service":
		// 客服消息：用户与商家、管理员的会话
		sessionTypes = []string{"user_to_merchant", "user_to_admin", "admin_to_merchant"}
	case "system":
		// 系统通知：不使用会话系统，直接返回空结果
		// 系统通知通过消息接口直接获取，不需要会话列表
		return []*dto.SessionDTO{}, 0, nil
	case "order":
		// 订单消息：使用订单通知会话类型
		sessionTypes = []string{"order_notification"}
	default:
		return nil, 0, fmt.Errorf("不支持的消息分类: %s", category)
	}

	// 获取指定类型的会话列表
	sessions, total, err := s.chatRepo.ListSessionsByUserAndTypes(ctx, userID, userType, sessionTypes, page, pageSize)
	if err != nil {
		logs.Error("[ChatService] 获取用户分类会话列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取用户分类会话列表失败: %v", err)
	}

	// 转换为DTO（复用GetUserSessions的逻辑）
	sessionDTOs := make([]*dto.SessionDTO, 0, len(sessions))
	for _, session := range sessions {
		sessionDTO := s.convertSessionToDTO(ctx, session, userID, userType)
		if sessionDTO != nil {
			sessionDTOs = append(sessionDTOs, sessionDTO)
		}
	}

	return sessionDTOs, total, nil
}

// convertSessionToDTO 将会话模型转换为DTO（提取公共逻辑）
func (s *ChatServiceImpl) convertSessionToDTO(ctx context.Context, session *models.ChatSession, currentUserID int64, currentUserType string) *dto.SessionDTO {
	sessionDTO := &dto.SessionDTO{
		ID:            session.ID,
		Type:          session.Type,
		CreatorID:     session.CreatorID,
		CreatorType:   session.CreatorType,
		ReceiverID:    session.ReceiverID,
		ReceiverType:  session.ReceiverType,
		LastMessageID: session.LastMessageID,
		UnreadCount:   session.UnreadCount,
		Status:        session.Status,
		CreatedAt:     session.CreatedAt,
		UpdatedAt:     session.UpdatedAt,
	}

	// 如果是群聊会话，设置群ID
	if session.Type == "group" {
		sessionDTO.GroupID = session.ReceiverID
	}

	// 获取最后一条消息
	if session.LastMessageID > 0 {
		lastMessage, err := s.chatRepo.GetMessageByID(ctx, session.LastMessageID)
		if err == nil && lastMessage != nil {
			messageDTO := &dto.MessageDTO{
				ID:         lastMessage.ID,
				SessionID:  lastMessage.SessionID,
				SenderID:   lastMessage.SenderID,
				SenderType: lastMessage.SenderType,
				Content:    lastMessage.Content,
				Type:       lastMessage.Type,
				ResourceID: lastMessage.ResourceID,
				Status:     lastMessage.Status,
				CreatedAt:  lastMessage.CreatedAt,
			}

			// 处理媒体消息的URL
			if lastMessage.Type == "image" || lastMessage.Type == "file" || lastMessage.Type == "voice" || lastMessage.Type == "video" {
				if lastMessage.ResourceID != "" {
					// 如果有ResourceID（上传文件的媒体消息），将文件路径转换为完整URL并放入Content字段
					fileURL := storage.GetFileURL(lastMessage.ResourceID)
					if fileURL != "" {
						messageDTO.Content = fileURL
						messageDTO.ResourceID = fileURL // 保持ResourceID也是URL，用于兼容性
						logs.Debug("[convertSessionToDTO] 最后消息URL转换: %s -> %s", lastMessage.ResourceID, fileURL)
					} else {
						logs.Warn("[convertSessionToDTO] 无法获取最后消息文件URL，文件路径: %s", lastMessage.ResourceID)
					}
				} else if lastMessage.Content != "" {
					// 如果没有ResourceID但有Content（URL媒体消息），Content已经是URL，保持不变
					messageDTO.ResourceID = lastMessage.Content // 保持ResourceID也是URL，用于兼容性
					logs.Debug("[convertSessionToDTO] 最后消息为URL媒体消息，Content已是URL: %s", lastMessage.Content)
				}
			}

			// 填充发送者信息
			s.fillSenderInfo(ctx, messageDTO)

			sessionDTO.LastMessage = messageDTO
		}
	}

	// 填充对话目标信息
	s.fillTargetInfo(ctx, sessionDTO, currentUserID, currentUserType)

	return sessionDTO
}

// fillTargetInfo 填充对话目标信息（提取公共逻辑）
func (s *ChatServiceImpl) fillTargetInfo(ctx context.Context, sessionDTO *dto.SessionDTO, currentUserID int64, currentUserType string) {
	// 添加详细的调试日志
	logs.Debug("[fillTargetInfo] 开始填充目标信息: sessionID=%d, type=%s, currentUserID=%d, currentUserType=%s",
		sessionDTO.ID, sessionDTO.Type, currentUserID, currentUserType)
	logs.Debug("[fillTargetInfo] 会话信息: CreatorID=%d, CreatorType=%s, ReceiverID=%d, ReceiverType=%s",
		sessionDTO.CreatorID, sessionDTO.CreatorType, sessionDTO.ReceiverID, sessionDTO.ReceiverType)

	if sessionDTO.Type == "group" {
		// 群聊会话：获取群组信息
		logs.Debug("[fillTargetInfo] 处理群聊会话，群ID=%d", sessionDTO.ReceiverID)
		if groupInfo, err := s.groupService.GetGroupByID(sessionDTO.ReceiverID); err == nil {
			sessionDTO.TargetName = groupInfo.Name
			sessionDTO.TargetAvatar = groupInfo.Avatar
			logs.Debug("[fillTargetInfo] 群聊信息获取成功: name=%s, avatar=%s", groupInfo.Name, groupInfo.Avatar)
		} else {
			logs.Error("[ChatService] 获取群组信息失败: %v", err)
			sessionDTO.TargetName = "未知群组"
		}
	} else {
		// 一对一会话：获取对话目标信息
		var targetID int64
		var targetType string

		// 确定对话目标（非当前用户的另一方）
		if sessionDTO.CreatorID == currentUserID && sessionDTO.CreatorType == currentUserType {
			targetID = sessionDTO.ReceiverID
			targetType = sessionDTO.ReceiverType
			logs.Debug("[fillTargetInfo] 当前用户是创建者，目标是接收者: targetID=%d, targetType=%s", targetID, targetType)
		} else {
			targetID = sessionDTO.CreatorID
			targetType = sessionDTO.CreatorType
			logs.Debug("[fillTargetInfo] 当前用户是接收者，目标是创建者: targetID=%d, targetType=%s", targetID, targetType)
		}

		// 检查异常情况并尝试修复
		if targetID == currentUserID {
			logs.Warn("[fillTargetInfo] 检测到异常：目标用户ID与当前用户ID相同，尝试修复")

			// 尝试修复：如果creator_id = receiver_id，说明数据异常
			if sessionDTO.CreatorID == sessionDTO.ReceiverID {
				logs.Error("[fillTargetInfo] 数据异常：creator_id与receiver_id相同，无法确定对话目标")
				sessionDTO.TargetName = "数据异常"
				sessionDTO.TargetAvatar = ""
				return
			}

			// 尝试修复：重新确定目标用户
			if sessionDTO.CreatorID != currentUserID {
				targetID = sessionDTO.CreatorID
				targetType = sessionDTO.CreatorType
				logs.Debug("[fillTargetInfo] 修复后目标用户: targetID=%d, targetType=%s", targetID, targetType)
			} else if sessionDTO.ReceiverID != currentUserID {
				targetID = sessionDTO.ReceiverID
				targetType = sessionDTO.ReceiverType
				logs.Debug("[fillTargetInfo] 修复后目标用户: targetID=%d, targetType=%s", targetID, targetType)
			} else {
				logs.Error("[fillTargetInfo] 无法修复：所有用户ID都相同")
				sessionDTO.TargetName = "无法确定对话对象"
				sessionDTO.TargetAvatar = ""
				return
			}
		}

		// 处理creator_type或receiver_type为"chat"的异常情况
		if targetType == "chat" {
			logs.Warn("[fillTargetInfo] 检测到异常的targetType: %s，尝试修复为user", targetType)
			targetType = "user"
		}

		// 根据目标类型获取信息
		if targetType == "user" {
			logs.Debug("[fillTargetInfo] 获取用户信息: userID=%d", targetID)
			if userInfo, err := s.userService.GetUserByID(ctx, targetID); err == nil {
				sessionDTO.TargetName = userInfo.Nickname
				if sessionDTO.TargetName == "" {
					sessionDTO.TargetName = userInfo.Username
				}
				sessionDTO.TargetAvatar = userInfo.Avatar
				logs.Debug("[fillTargetInfo] 用户信息获取成功: name=%s, avatar=%s", sessionDTO.TargetName, sessionDTO.TargetAvatar)

				// 如果当前用户不是普通用户，则获取客户在线状态信息
				if currentUserType != "user" {
					customerInfo := &dto.CustomerOnlineInfo{
						ID:     userInfo.ID,
						Name:   sessionDTO.TargetName,
						Avatar: userInfo.Avatar,
					}

					// 获取在线状态
					if onlineInfo, err := s.onlineStatusService.GetUserOnlineInfo(ctx, targetID, "user"); err == nil {
						customerInfo.IsOnline = true
						customerInfo.OnlineStatus = string(onlineInfo.Status)
						lastSeen := onlineInfo.LastActivity.Format("2006-01-02T15:04:05Z07:00")
						customerInfo.LastSeen = &lastSeen
					} else {
						customerInfo.IsOnline = false
						customerInfo.OnlineStatus = "offline"
					}

					sessionDTO.CustomerInfo = customerInfo
				}
			} else {
				logs.Error("[ChatService] 获取用户信息失败: targetID=%d, error=%v", targetID, err)
				sessionDTO.TargetName = "未知用户"
			}
		} else if targetType == "merchant" {
			logs.Debug("[fillTargetInfo] 获取商家信息: merchantID=%d", targetID)
			if merchantInfo, err := s.merchantService.GetMerchantByID(ctx, targetID); err == nil {
				sessionDTO.TargetName = merchantInfo.Name
				sessionDTO.TargetAvatar = merchantInfo.Logo
				logs.Debug("[fillTargetInfo] 商家信息获取成功: name=%s, avatar=%s", merchantInfo.Name, merchantInfo.Logo)
			} else {
				logs.Error("[ChatService] 获取商户信息失败: targetID=%d, error=%v", targetID, err)
				sessionDTO.TargetName = "未知商户"
			}
		} else if targetType == "admin" {
			logs.Debug("[fillTargetInfo] 获取管理员信息: adminID=%d", targetID)
			if adminInfo, err := s.adminService.GetAdminByID(ctx, targetID); err == nil {
				sessionDTO.TargetName = adminInfo.Nickname
				if sessionDTO.TargetName == "" {
					sessionDTO.TargetName = adminInfo.Username
				}
				sessionDTO.TargetAvatar = adminInfo.Avatar
				logs.Debug("[fillTargetInfo] 管理员信息获取成功: name=%s, avatar=%s", sessionDTO.TargetName, sessionDTO.TargetAvatar)
			} else {
				logs.Error("[ChatService] 获取管理员信息失败: targetID=%d, error=%v", targetID, err)
				sessionDTO.TargetName = "未知管理员"
			}
		} else {
			logs.Warn("[fillTargetInfo] 未知的目标类型: %s", targetType)
			sessionDTO.TargetName = "未知用户"
		}
	}

	logs.Debug("[fillTargetInfo] 填充完成: TargetName=%s, TargetAvatar=%s", sessionDTO.TargetName, sessionDTO.TargetAvatar)
}

// FillSenderInfo 填充消息发送者信息（公共方法）
func (s *ChatServiceImpl) FillSenderInfo(ctx context.Context, messageDTO *dto.MessageDTO) {
	s.fillSenderInfo(ctx, messageDTO)
}

// fillSenderInfo 填充消息发送者信息
func (s *ChatServiceImpl) fillSenderInfo(ctx context.Context, messageDTO *dto.MessageDTO) {
	switch messageDTO.SenderType {
	case "user":
		if userInfo, err := s.userService.GetUserByID(ctx, messageDTO.SenderID); err == nil {
			messageDTO.SenderName = userInfo.Nickname
			if messageDTO.SenderName == "" {
				messageDTO.SenderName = userInfo.Username
			}
			messageDTO.SenderAvatar = userInfo.Avatar
		} else {
			logs.Error("[ChatService] 获取用户信息失败: %v", err)
			messageDTO.SenderName = "未知用户"
		}
	case "merchant":
		if merchantInfo, err := s.merchantService.GetMerchantByID(ctx, messageDTO.SenderID); err == nil {
			messageDTO.SenderName = merchantInfo.Name
			messageDTO.SenderAvatar = merchantInfo.Logo
		} else {
			logs.Error("[ChatService] 获取商户信息失败: %v", err)
			messageDTO.SenderName = "未知商户"
		}
	case "admin":
		// 管理员信息处理 - 获取实际的管理员信息
		if adminInfo, err := s.adminService.GetAdminByID(ctx, messageDTO.SenderID); err == nil {
			messageDTO.SenderName = adminInfo.Nickname
			if messageDTO.SenderName == "" {
				messageDTO.SenderName = adminInfo.Username
			}
			messageDTO.SenderAvatar = adminInfo.Avatar
		} else {
			logs.Error("[ChatService] 获取管理员信息失败: %v", err)
			messageDTO.SenderName = "客服"
			messageDTO.SenderAvatar = "" // 设置默认客服头像
		}
	case "system":
		// 系统消息
		messageDTO.SenderName = "系统"
		messageDTO.SenderAvatar = "" // 可以设置默认系统头像
	default:
		messageDTO.SenderName = "未知发送者"
	}
}

// SendTextMessage 发送文本消息
func (s *ChatServiceImpl) SendTextMessage(
	ctx context.Context,
	sessionID int64,
	senderID int64,
	senderType string,
	content string,
) (*models.ChatMessage, error) {
	// 检查会话是否存在
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		logs.Error("[ChatService] 获取会话失败: %v", err)
		return nil, fmt.Errorf("获取会话失败: %v", err)
	}
	if session == nil {
		return nil, fmt.Errorf("会话不存在")
	}

	// 创建消息
	message := &models.ChatMessage{
		SessionID:  sessionID,
		SenderID:   senderID,
		SenderType: senderType,
		Content:    content,
		Type:       models.MessageTypeText,
		Status:     0, // 未读状态
	}

	// 保存消息
	id, err := s.chatRepo.CreateMessage(ctx, message)
	if err != nil {
		logs.Error("[ChatService] 创建消息失败: %v", err)
		return nil, fmt.Errorf("创建消息失败: %v", err)
	}

	// 更新会话的最后一条消息ID
	err = s.chatRepo.UpdateSessionLastMessage(ctx, sessionID, id)
	if err != nil {
		logs.Error("[ChatService] 更新会话最后消息ID失败: %v", err)
		// 不阻止消息返回
	}

	// 统计未读消息数量
	if session.Type == models.SessionTypeGroup {
		// 群聊：为所有群成员（除发送者外）更新未读计数
		logs.Info("[ChatService] 处理群聊消息未读计数，群聊ID: %d", session.ReceiverID)

		// 获取群成员列表
		o := orm.NewOrm()
		var members []models.ChatGroupMember
		_, err := o.QueryTable(new(models.ChatGroupMember)).
			Filter("group_id", session.ReceiverID).
			Filter("status", 0). // 只获取正常状态的成员
			All(&members)

		if err != nil {
			logs.Error("[ChatService] 获取群成员列表失败: %v", err)
		} else {
			// 为每个群成员（除发送者外）更新未读计数
			for _, member := range members {
				if member.UserID != senderID || member.UserType != senderType {
					unreadCount, _ := s.chatRepo.CountUnreadMessages(ctx, sessionID, member.UserID)
					logs.Debug("[ChatService] 群成员 %d(%s) 未读消息数: %d", member.UserID, member.UserType, unreadCount)
				}
			}

			// 更新会话的总未读计数（这里可以设置为最新消息的总数或其他逻辑）
			totalUnread := len(members) - 1 // 简化处理：成员数减去发送者
			if totalUnread < 0 {
				totalUnread = 0
			}
			if err = s.chatRepo.UpdateSessionUnreadCount(ctx, sessionID, totalUnread); err != nil {
				logs.Error("[ChatService] 更新群聊会话未读消息计数失败: %v", err)
			}
		}
	} else {
		// 一对一聊天：原有逻辑
		var recipientID int64
		if session.CreatorID == senderID {
			recipientID = session.ReceiverID
		} else {
			recipientID = session.CreatorID
		}

		// 统计并更新未读数量
		unreadCount, _ := s.chatRepo.CountUnreadMessages(ctx, sessionID, recipientID)
		if err = s.chatRepo.UpdateSessionUnreadCount(ctx, sessionID, unreadCount); err != nil {
			logs.Error("[ChatService] 更新未读消息计数失败: %v", err)
			// 不阻止消息返回
		}
	}

	// 获取完整的消息信息
	message, err = s.chatRepo.GetMessageByID(ctx, id)
	if err != nil {
		logs.Error("[ChatService] 获取创建的消息失败: %v", err)
		return nil, fmt.Errorf("获取创建的消息失败: %v", err)
	}

	return message, nil
}

// SendMediaMessage 发送媒体消息（图片、文件、语音）
func (s *ChatServiceImpl) SendMediaMessage(
	ctx context.Context,
	sessionID int64,
	senderID int64,
	senderType string,
	mediaType string,
	resourceID string,
) (*models.ChatMessage, error) {
	// 检查会话是否存在
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		logs.Error("[ChatService] 获取会话失败: %v", err)
		return nil, fmt.Errorf("获取会话失败: %v", err)
	}
	if session == nil {
		return nil, fmt.Errorf("会话不存在")
	}

	// 验证媒体类型
	validTypes := map[string]bool{
		models.MessageTypeImage: true,
		models.MessageTypeFile:  true,
		models.MessageTypeVoice: true,
		models.MessageTypeVideo: true,
	}
	if !validTypes[mediaType] {
		return nil, fmt.Errorf("不支持的媒体类型: %s", mediaType)
	}

	// 创建消息
	message := &models.ChatMessage{
		SessionID:  sessionID,
		SenderID:   senderID,
		SenderType: senderType,
		Content:    "", // 媒体消息内容为空
		Type:       mediaType,
		ResourceID: resourceID,
		Status:     0, // 未读状态
	}

	// 保存消息
	id, err := s.chatRepo.CreateMessage(ctx, message)
	if err != nil {
		logs.Error("[ChatService] 创建媒体消息失败: %v", err)
		return nil, fmt.Errorf("创建媒体消息失败: %v", err)
	}

	// 更新会话的最后一条消息ID
	err = s.chatRepo.UpdateSessionLastMessage(ctx, sessionID, id)
	if err != nil {
		logs.Error("[ChatService] 更新会话最后消息ID失败: %v", err)
		// 不阻止消息返回
	}

	// 统计未读消息数量
	if session.Type == models.SessionTypeGroup {
		// 群聊：为所有群成员（除发送者外）更新未读计数
		logs.Info("[ChatService] 处理群聊媒体消息未读计数，群聊ID: %d", session.ReceiverID)

		// 获取群成员列表
		o := orm.NewOrm()
		var members []models.ChatGroupMember
		_, err := o.QueryTable(new(models.ChatGroupMember)).
			Filter("group_id", session.ReceiverID).
			Filter("status", 0). // 只获取正常状态的成员
			All(&members)

		if err != nil {
			logs.Error("[ChatService] 获取群成员列表失败: %v", err)
		} else {
			// 为每个群成员（除发送者外）更新未读计数
			for _, member := range members {
				if member.UserID != senderID || member.UserType != senderType {
					unreadCount, _ := s.chatRepo.CountUnreadMessages(ctx, sessionID, member.UserID)
					logs.Debug("[ChatService] 群成员 %d(%s) 未读消息数: %d", member.UserID, member.UserType, unreadCount)
				}
			}

			// 更新会话的总未读计数
			totalUnread := len(members) - 1 // 简化处理：成员数减去发送者
			if totalUnread < 0 {
				totalUnread = 0
			}
			if err = s.chatRepo.UpdateSessionUnreadCount(ctx, sessionID, totalUnread); err != nil {
				logs.Error("[ChatService] 更新群聊会话未读消息计数失败: %v", err)
			}
		}
	} else {
		// 一对一聊天：原有逻辑
		var recipientID int64
		if session.CreatorID == senderID {
			recipientID = session.ReceiverID
		} else {
			recipientID = session.CreatorID
		}

		// 统计并更新未读数量
		unreadCount, _ := s.chatRepo.CountUnreadMessages(ctx, sessionID, recipientID)
		if err = s.chatRepo.UpdateSessionUnreadCount(ctx, sessionID, unreadCount); err != nil {
			logs.Error("[ChatService] 更新未读消息计数失败: %v", err)
			// 不阻止消息返回
		}
	}

	// 获取完整的消息信息
	message, err = s.chatRepo.GetMessageByID(ctx, id)
	if err != nil {
		logs.Error("[ChatService] 获取创建的媒体消息失败: %v", err)
		return nil, fmt.Errorf("获取创建的媒体消息失败: %v", err)
	}

	return message, nil
}

// SendMediaURLMessage 发送媒体URL消息（处理已上传的媒体文件URL）
func (s *ChatServiceImpl) SendMediaURLMessage(
	ctx context.Context,
	sessionID int64,
	senderID int64,
	senderType string,
	mediaType string,
	mediaURL string,
	fileName string,
	fileSize int64,
	fileType string,
	fileExt string,
) (*models.ChatMessage, error) {
	// 检查会话是否存在
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		logs.Error("[ChatService] 获取会话失败: %v", err)
		return nil, fmt.Errorf("获取会话失败: %v", err)
	}
	if session == nil {
		return nil, fmt.Errorf("会话不存在")
	}

	// 验证媒体类型
	validTypes := map[string]bool{
		models.MessageTypeImage: true,
		models.MessageTypeFile:  true,
		models.MessageTypeVoice: true,
		models.MessageTypeVideo: true,
	}
	if !validTypes[mediaType] {
		return nil, fmt.Errorf("不支持的媒体类型: %s", mediaType)
	}

	// 创建消息
	message := &models.ChatMessage{
		SessionID:  sessionID,
		SenderID:   senderID,
		SenderType: senderType,
		Content:    mediaURL, // 媒体URL消息将URL存储在Content字段
		Type:       mediaType,
		ResourceID: "", // URL消息不使用ResourceID
		FileName:   fileName,
		FileSize:   fileSize,
		FileType:   fileType,
		FileExt:    fileExt,
		Status:     0, // 未读状态
	}

	// 保存消息
	id, err := s.chatRepo.CreateMessage(ctx, message)
	if err != nil {
		logs.Error("[ChatService] 创建媒体URL消息失败: %v", err)
		return nil, fmt.Errorf("创建媒体URL消息失败: %v", err)
	}

	// 更新会话的最后一条消息ID
	err = s.chatRepo.UpdateSessionLastMessage(ctx, sessionID, id)
	if err != nil {
		logs.Error("[ChatService] 更新会话最后消息ID失败: %v", err)
		// 不阻止消息返回
	}

	// 统计未读消息数量
	if session.Type == models.SessionTypeGroup {
		// 群聊：为所有群成员（除发送者外）更新未读计数
		logs.Info("[ChatService] 处理群聊媒体URL消息未读计数，群聊ID: %d", session.ReceiverID)

		// 获取群成员列表
		o := orm.NewOrm()
		var members []models.ChatGroupMember
		_, err := o.QueryTable(new(models.ChatGroupMember)).
			Filter("group_id", session.ReceiverID).
			Filter("status", 0). // 只获取正常状态的成员
			All(&members)

		if err != nil {
			logs.Error("[ChatService] 获取群成员列表失败: %v", err)
		} else {
			// 为每个群成员（除发送者外）更新未读计数
			for _, member := range members {
				if member.UserID != senderID || member.UserType != senderType {
					unreadCount, _ := s.chatRepo.CountUnreadMessages(ctx, sessionID, member.UserID)
					logs.Debug("[ChatService] 群成员 %d(%s) 未读消息数: %d", member.UserID, member.UserType, unreadCount)
				}
			}

			// 更新会话的总未读计数
			totalUnread := len(members) - 1 // 简化处理：成员数减去发送者
			if totalUnread < 0 {
				totalUnread = 0
			}
			if err = s.chatRepo.UpdateSessionUnreadCount(ctx, sessionID, totalUnread); err != nil {
				logs.Error("[ChatService] 更新群聊会话未读消息计数失败: %v", err)
			}
		}
	} else {
		// 一对一聊天：原有逻辑
		var recipientID int64
		if session.CreatorID == senderID {
			recipientID = session.ReceiverID
		} else {
			recipientID = session.CreatorID
		}

		// 统计并更新未读数量
		unreadCount, _ := s.chatRepo.CountUnreadMessages(ctx, sessionID, recipientID)
		if err = s.chatRepo.UpdateSessionUnreadCount(ctx, sessionID, unreadCount); err != nil {
			logs.Error("[ChatService] 更新未读消息计数失败: %v", err)
			// 不阻止消息返回
		}
	}

	// 获取完整的消息信息
	message, err = s.chatRepo.GetMessageByID(ctx, id)
	if err != nil {
		logs.Error("[ChatService] 获取创建的媒体URL消息失败: %v", err)
		return nil, fmt.Errorf("获取创建的媒体URL消息失败: %v", err)
	}

	return message, nil
}

// GetMessage 获取消息详情
func (s *ChatServiceImpl) GetMessage(ctx context.Context, messageID int64) (*models.ChatMessage, error) {
	message, err := s.chatRepo.GetMessageByID(ctx, messageID)
	if err != nil {
		logs.Error("[ChatService] 获取消息失败: %v", err)
		return nil, fmt.Errorf("获取消息失败: %v", err)
	}
	if message == nil {
		return nil, fmt.Errorf("消息不存在")
	}
	return message, nil
}

// GetSessionMessages 获取会话消息列表
func (s *ChatServiceImpl) GetSessionMessages(
	ctx context.Context,
	sessionID int64,
	page, pageSize int,
	order string,
) ([]*dto.MessageDTO, int64, error) {
	// 检查会话是否存在
	session, err := s.chatRepo.GetSessionByID(ctx, sessionID)
	if err != nil {
		logs.Error("[ChatService] 获取会话失败: %v", err)
		return nil, 0, fmt.Errorf("获取会话失败: %v", err)
	}
	if session == nil {
		return nil, 0, fmt.Errorf("会话不存在")
	}

	// 获取消息列表
	messages, total, err := s.chatRepo.ListMessagesBySession(ctx, sessionID, page, pageSize, order)
	if err != nil {
		logs.Error("[ChatService] 获取会话消息列表失败: %v", err)
		return nil, 0, fmt.Errorf("获取会话消息列表失败: %v", err)
	}

	// 转换为DTO
	messageDTOs := make([]*dto.MessageDTO, 0, len(messages))
	for _, message := range messages {
		// 添加调试日志
		logs.Debug("[GetSessionMessages] 消息ID: %d, 原始Type: %s, Content: %s", message.ID, message.Type, message.Content)

		messageDTO := &dto.MessageDTO{
			ID:         message.ID,
			SessionID:  message.SessionID,
			SenderID:   message.SenderID,
			SenderType: message.SenderType,
			Content:    message.Content,
			Type:       message.Type,
			ResourceID: message.ResourceID,
			FileName:   message.FileName,
			FileSize:   message.FileSize,
			FileType:   message.FileType,
			FileExt:    message.FileExt,
			Status:     message.Status,
			CreatedAt:  message.CreatedAt,

			// 通知消息相关字段
			NotificationType: message.NotificationType,
			Priority:         message.Priority,
			Persistent:       message.Persistent,
			ExpireAt:         message.ExpireAt,
		}

		// 解析NotificationData JSON字符串为对象
		if message.NotificationData != "" {
			var notificationData interface{}
			if err := json.Unmarshal([]byte(message.NotificationData), &notificationData); err != nil {
				logs.Warn("[GetSessionMessages] 解析NotificationData失败: %v, 原始数据: %s", err, message.NotificationData)
				// 解析失败时，将原始字符串作为数据返回
				messageDTO.NotificationData = message.NotificationData
			} else {
				messageDTO.NotificationData = notificationData
				logs.Debug("[GetSessionMessages] NotificationData解析成功: %+v", notificationData)
			}
		}

		// 处理媒体消息的URL
		if message.Type == "image" || message.Type == "file" || message.Type == "voice" || message.Type == "video" {
			if message.ResourceID != "" {
				// 如果有ResourceID（上传文件的媒体消息），将文件路径转换为完整URL并放入Content字段
				fileURL := storage.GetFileURL(message.ResourceID)
				if fileURL != "" {
					messageDTO.Content = fileURL
					messageDTO.ResourceID = fileURL // 保持ResourceID也是URL，用于兼容性
					logs.Debug("[GetSessionMessages] 媒体消息URL转换: %s -> %s", message.ResourceID, fileURL)
				} else {
					logs.Warn("[GetSessionMessages] 无法获取文件URL，文件路径: %s", message.ResourceID)
				}
			} else if message.Content != "" {
				// 如果没有ResourceID但有Content（URL媒体消息），Content已经是URL，保持不变
				messageDTO.ResourceID = message.Content // 保持ResourceID也是URL，用于兼容性
				logs.Debug("[GetSessionMessages] URL媒体消息，Content已是URL: %s", message.Content)
			}
		}

		// 添加调试日志
		logs.Debug("[GetSessionMessages] DTO转换后 - 消息ID: %d, DTO Type: %s", messageDTO.ID, messageDTO.Type)

		// 填充发送者信息
		switch message.SenderType {
		case "user":
			// 获取用户信息
			if user, err := s.userService.GetUserByID(ctx, message.SenderID); err == nil {
				messageDTO.SenderName = user.Nickname
				messageDTO.SenderAvatar = user.Avatar
			} else {
				logs.Warn("[ChatService] 获取用户信息失败: %v", err)
			}
		case "merchant":
			// 获取商户信息
			if merchant, err := s.merchantService.GetMerchantByID(ctx, message.SenderID); err == nil {
				messageDTO.SenderName = merchant.Name
				messageDTO.SenderAvatar = merchant.Logo
			} else {
				logs.Warn("[ChatService] 获取商户信息失败: %v", err)
			}
		case "admin":
			// 获取管理员信息
			if admin, err := s.adminService.GetAdminByID(ctx, message.SenderID); err == nil {
				messageDTO.SenderName = admin.Nickname
				if messageDTO.SenderName == "" {
					messageDTO.SenderName = admin.Username
				}
				messageDTO.SenderAvatar = admin.Avatar
			} else {
				logs.Warn("[ChatService] 获取管理员信息失败: %v", err)
				messageDTO.SenderName = "客服"
				messageDTO.SenderAvatar = ""
			}
		case "system":
			// 系统消息
			messageDTO.SenderName = "系统"
			messageDTO.SenderAvatar = ""
		default:
			logs.Warn("[ChatService] 未知的发送者类型: %s", message.SenderType)
		}

		messageDTOs = append(messageDTOs, messageDTO)
	}

	return messageDTOs, total, nil
}

// MarkMessagesAsRead 标记会话消息为已读
func (s *ChatServiceImpl) MarkMessagesAsRead(ctx context.Context, sessionID int64, userID int64) error {
	// 标记会话中的消息为已读
	err := s.chatRepo.MarkSessionMessagesAsRead(ctx, sessionID, userID)
	if err != nil {
		logs.Error("[ChatService] 标记会话消息为已读失败: %v", err)
		return fmt.Errorf("标记会话消息为已读失败: %v", err)
	}

	// 重置会话的未读计数
	err = s.chatRepo.ResetSessionUnreadCount(ctx, sessionID)
	if err != nil {
		logs.Error("[ChatService] 重置会话未读计数失败: %v", err)
		return fmt.Errorf("重置会话未读计数失败: %v", err)
	}

	return nil
}

// CreateGroupSession 创建群聊会话
func (s *ChatServiceImpl) CreateGroupSession(
	ctx context.Context,
	groupID int64,
	creatorID int64,
	creatorType string,
) (*models.ChatSession, error) {
	// 创建群聊会话
	session := &models.ChatSession{
		Type:         models.SessionTypeGroup,
		CreatorID:    creatorID,
		CreatorType:  creatorType,
		ReceiverID:   groupID, // 群聊ID作为接收者ID
		ReceiverType: "group",
		Status:       0, // 正常状态
	}

	// 保存会话
	id, err := s.chatRepo.CreateSession(ctx, session)
	if err != nil {
		logs.Error("[ChatService] 创建群聊会话失败: %v", err)
		return nil, fmt.Errorf("创建群聊会话失败: %v", err)
	}

	// 查询完整会话信息
	session, err = s.chatRepo.GetSessionByID(ctx, id)
	if err != nil {
		logs.Error("[ChatService] 获取创建的群聊会话失败: %v", err)
		return nil, fmt.Errorf("获取创建的群聊会话失败: %v", err)
	}

	return session, nil
}

// JoinGroupSession 加入群聊会话
func (s *ChatServiceImpl) JoinGroupSession(
	ctx context.Context,
	groupID int64,
	userID int64,
	userType string,
) (*models.ChatSession, error) {
	o := orm.NewOrm()

	// 首先查找群聊信息，获取关联的会话ID
	group := &models.ChatGroup{ID: groupID}
	err := o.Read(group)
	if err != nil {
		logs.Error("[ChatService] 获取群聊信息失败: %v", err)
		return nil, fmt.Errorf("群聊不存在或已解散")
	}

	// 添加调试日志
	logs.Info("[ChatService] 群聊 %d 信息: Name=%s, SessionID=%d, Status=%d", groupID, group.Name, group.SessionID, group.Status)

	// 检查群聊状态
	if group.Status != 0 {
		return nil, fmt.Errorf("群聊已解散")
	}

	var groupSession *models.ChatSession

	// 如果群聊已有关联的会话ID，直接获取会话
	if group.SessionID > 0 {
		logs.Info("[ChatService] 群聊 %d 已有关联会话ID: %d，正在获取会话详情", groupID, group.SessionID)
		groupSession, err = s.chatRepo.GetSessionByID(ctx, group.SessionID)
		if err != nil {
			logs.Error("[ChatService] 获取群聊会话失败: %v", err)
			return nil, fmt.Errorf("获取群聊会话失败: %v", err)
		}

		// 添加调试日志
		if groupSession != nil {
			logs.Info("[ChatService] 找到会话: ID=%d, Type=%s, ReceiverID=%d, Status=%d", groupSession.ID, groupSession.Type, groupSession.ReceiverID, groupSession.Status)

			// 验证会话类型和接收者ID是否匹配
			if groupSession.Type != models.SessionTypeGroup {
				logs.Error("[ChatService] 群聊 %d 关联的会话 %d 类型错误: %s，期望: %s", groupID, group.SessionID, groupSession.Type, models.SessionTypeGroup)
				logs.Info("[ChatService] 群聊 %d 的会话类型不匹配，重新创建群聊会话", groupID)
				groupSession = nil
			} else if groupSession.ReceiverID != groupID {
				logs.Error("[ChatService] 群聊 %d 关联的会话 %d 接收者ID错误: %d，期望: %d", groupID, group.SessionID, groupSession.ReceiverID, groupID)
				logs.Info("[ChatService] 群聊 %d 的会话接收者ID不匹配，重新创建群聊会话", groupID)
				groupSession = nil
			}
		} else {
			logs.Info("[ChatService] 会话ID %d 对应的会话不存在", group.SessionID)
		}

		// 如果会话不存在、已关闭、类型不匹配或接收者ID不匹配，需要重新创建
		if groupSession == nil || groupSession.Status != 0 {
			logs.Info("[ChatService] 群聊 %d 的会话已失效，重新创建", groupID)
			groupSession = nil
		} else {
			logs.Info("[ChatService] 用户 %d (%s) 加入群聊 %d 的现有会话 %d", userID, userType, groupID, groupSession.ID)
			return groupSession, nil
		}
	} else {
		logs.Info("[ChatService] 群聊 %d 没有关联的会话ID，需要创建新会话", groupID)
	}

	// 创建新的群聊会话
	logs.Info("[ChatService] 为群聊 %d 创建新会话", groupID)
	groupSession, err = s.CreateGroupSession(ctx, groupID, userID, userType)
	if err != nil {
		logs.Error("[ChatService] 创建群聊会话失败: %v", err)
		return nil, fmt.Errorf("创建群聊会话失败: %v", err)
	}

	// 更新群聊的会话ID
	group.SessionID = groupSession.ID
	_, err = o.Update(group, "session_id")
	if err != nil {
		logs.Error("[ChatService] 更新群聊会话ID失败: %v", err)
		// 这里不返回错误，因为会话已经创建成功
	}

	logs.Info("[ChatService] 用户 %d (%s) 成功加入群聊 %d 的会话 %d", userID, userType, groupID, groupSession.ID)

	// 如果用户当前在线，自动订阅该群聊会话
	s.subscribeUserToGroupSession(userID, userType, groupSession.ID)

	return groupSession, nil
}

// subscribeUserToGroupSession 订阅用户到群聊会话
func (s *ChatServiceImpl) subscribeUserToGroupSession(userID int64, userType string, sessionID int64) {
	if s.wsManager == nil {
		logs.Debug("[ChatService] WebSocket管理器为空，跳过自动订阅")
		return
	}

	// 检查用户是否在线
	client := s.wsManager.GetClient(userID, userType)
	if client == nil {
		logs.Debug("[ChatService] 用户 %d(%s) 不在线，跳过自动订阅会话 %d", userID, userType, sessionID)
		return
	}

	// 将用户添加到会话订阅中
	s.wsManager.AddToSession(sessionID, client)
	logs.Info("[ChatService] 用户 %d(%s) 已自动订阅群聊会话 %d", userID, userType, sessionID)
}

// LeaveGroupSession 离开群聊会话
func (s *ChatServiceImpl) LeaveGroupSession(
	ctx context.Context,
	groupID int64,
	userID int64,
	userType string,
) error {
	// 这里主要是记录用户离开群聊的逻辑
	// 实际的群成员管理由GroupMemberService处理
	// 这个方法可以用于记录会话相关的操作日志或通知
	logs.Info("[ChatService] 用户 %d (%s) 离开群聊 %d", userID, userType, groupID)
	return nil
}

// GetGroupSessionDetails 获取群聊会话详情
func (s *ChatServiceImpl) GetGroupSessionDetails(
	ctx context.Context,
	groupID int64,
	userID int64,
	userType string,
) (*dto.SessionDTO, error) {
	// 通过群ID查找对应的会话
	sessions, _, err := s.chatRepo.ListSessionsByUser(ctx, userID, userType, 1, 100)
	if err != nil {
		logs.Error("[ChatService] 获取用户会话列表失败: %v", err)
		return nil, fmt.Errorf("获取用户会话列表失败: %v", err)
	}

	// 查找群聊会话
	var groupSession *models.ChatSession
	for _, session := range sessions {
		if session.Type == models.SessionTypeGroup && session.ReceiverID == groupID {
			groupSession = session
			break
		}
	}

	if groupSession == nil {
		return nil, fmt.Errorf("未找到群聊会话")
	}

	// 转换为DTO
	sessionDTO := &dto.SessionDTO{
		ID:            groupSession.ID,
		Type:          groupSession.Type,
		CreatorID:     groupSession.CreatorID,
		CreatorType:   groupSession.CreatorType,
		ReceiverID:    groupSession.ReceiverID,
		ReceiverType:  groupSession.ReceiverType,
		LastMessageID: groupSession.LastMessageID,
		UnreadCount:   groupSession.UnreadCount,
		Status:        groupSession.Status,
		CreatedAt:     groupSession.CreatedAt,
		UpdatedAt:     groupSession.UpdatedAt,
	}

	// 如果是群聊会话，设置群ID
	if groupSession.Type == "group" {
		sessionDTO.GroupID = groupSession.ReceiverID
	}

	// 获取最后一条消息
	if groupSession.LastMessageID > 0 {
		lastMessage, err := s.chatRepo.GetMessageByID(ctx, groupSession.LastMessageID)
		if err == nil && lastMessage != nil {
			sessionDTO.LastMessage = &dto.MessageDTO{
				ID:         lastMessage.ID,
				SessionID:  lastMessage.SessionID,
				SenderID:   lastMessage.SenderID,
				SenderType: lastMessage.SenderType,
				Content:    lastMessage.Content,
				Type:       lastMessage.Type,
				ResourceID: lastMessage.ResourceID,
				Status:     lastMessage.Status,
				CreatedAt:  lastMessage.CreatedAt,
			}
		}
	}

	// TODO: 填充群聊名称和头像信息
	// 这里需要集成GroupService来获取群聊详细信息

	return sessionDTO, nil
}
