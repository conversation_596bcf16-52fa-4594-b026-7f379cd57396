/**
 * async_message_test.go
 * 异步消息服务测试文件
 *
 * 该文件用于测试异步消息保存功能，确保WebSocket消息能够正确异步保存到数据库
 */

package services

import (
	"context"
	"o_mall_backend/modules/chat/dto"
	"testing"
	"time"
)

// TestAsyncMessageServiceCreation 测试异步消息服务创建
func TestAsyncMessageServiceCreation(t *testing.T) {
	// 测试服务创建
	service := NewAsyncMessageService()
	if service == nil {
		t.Fatal("创建异步消息服务失败")
	}

	t.Log("异步消息服务创建成功")
}

// TestAsyncMessageTaskCreation 测试异步消息任务创建
func TestAsyncMessageTaskCreation(t *testing.T) {
	// 测试通知消息任务
	t.Run("NotificationTask", func(t *testing.T) {
		wsMessage := &dto.WebSocketMessageDTO{
			Type:      "notification",
			Event:     "test_notification",
			Timestamp: time.Now().Unix(),
			Data: map[string]interface{}{
				"title":   "测试通知",
				"content": "这是一条测试通知消息",
			},
		}

		task := &AsyncMessageTask{
			MessageType: "notification",
			SenderID:    0,
			SenderType:  "system",
			TargetID:    1001,
			TargetType:  "user",
			Content:     "测试通知消息",
			WSMessage:   wsMessage,
			NotifyData: map[string]interface{}{
				"title":   "测试通知",
				"content": "这是一条测试通知消息",
			},
		}

		if task.MessageType != "notification" {
			t.Errorf("期望消息类型为 notification，实际为 %s", task.MessageType)
		}
		if task.TargetID != 1001 {
			t.Errorf("期望目标ID为 1001，实际为 %d", task.TargetID)
		}
		if task.Content != "测试通知消息" {
			t.Errorf("期望内容为 '测试通知消息'，实际为 '%s'", task.Content)
		}

		t.Log("通知消息任务创建成功")
	})

	// 测试文本消息任务
	t.Run("TextTask", func(t *testing.T) {
		wsMessage := &dto.WebSocketMessageDTO{
			Type:      "message",
			Event:     "text_message",
			Timestamp: time.Now().Unix(),
			Data: map[string]interface{}{
				"sender_id":    1001,
				"sender_type":  "user",
				"target_id":    1002,
				"target_type":  "user",
				"content":      "Hello, World!",
				"message_type": "text",
			},
		}

		task := &AsyncMessageTask{
			MessageType: "text",
			SenderID:    1001,
			SenderType:  "user",
			TargetID:    1002,
			TargetType:  "user",
			Content:     "Hello, World!",
			WSMessage:   wsMessage,
		}

		if task.MessageType != "text" {
			t.Errorf("期望消息类型为 text，实际为 %s", task.MessageType)
		}
		if task.SenderID != 1001 {
			t.Errorf("期望发送者ID为 1001，实际为 %d", task.SenderID)
		}
		if task.Content != "Hello, World!" {
			t.Errorf("期望内容为 'Hello, World!'，实际为 '%s'", task.Content)
		}

		t.Log("文本消息任务创建成功")
	})
}

// TestWebSocketWithAsyncSave 测试WebSocket服务与异步保存的集成
func TestWebSocketWithAsyncSave(t *testing.T) {
	// 初始化服务容器
	container := GetServiceContainer()

	// 创建并启动异步消息服务
	asyncService := NewAsyncMessageService()
	err := asyncService.Start()
	if err != nil {
		t.Fatalf("启动异步消息服务失败: %v", err)
	}
	defer asyncService.Stop()

	container.SetAsyncMessageService(asyncService)

	// 测试用户WebSocket服务
	t.Run("UserWebSocketService", func(t *testing.T) {
		userService := NewWebSocketUserService()
		ctx := context.Background()

		// 发送订单支付成功通知（这会触发异步保存）
		err := userService.SendOrderPaymentSuccessNotification(ctx, 1001, 2001, "ORD20250129001", 99.99)
		// 由于WebSocket管理器未初始化，这里会返回错误，但异步保存逻辑应该不会执行
		if err == nil {
			t.Log("用户WebSocket服务测试通过")
		} else {
			t.Logf("用户WebSocket服务测试预期错误: %v", err)
		}
	})

	// 测试商家WebSocket服务
	t.Run("MerchantWebSocketService", func(t *testing.T) {
		merchantService := NewWebSocketMerchantService()
		ctx := context.Background()

		customerInfo := map[string]interface{}{
			"user_id":   1001,
			"user_name": "张三",
			"phone":     "13800138000",
		}

		// 发送新订单通知（这会触发异步保存）
		err := merchantService.SendNewOrderNotification(ctx, 2001, 3001, "ORD20250129001", 99.99, customerInfo)
		// 由于WebSocket管理器未初始化，这里会返回错误，但异步保存逻辑应该不会执行
		if err == nil {
			t.Log("商家WebSocket服务测试通过")
		} else {
			t.Logf("商家WebSocket服务测试预期错误: %v", err)
		}
	})

	// 等待异步任务完成
	time.Sleep(200 * time.Millisecond)
}
