/**
 * websocket_user.go
 * 用户/跑腿员WebSocket通知功能
 *
 * 本文件实现了用户和跑腿员专用的WebSocket通知功能，包括订单状态更新、支付成功通知、
 * 配送进度更新、跑腿员任务分配等用户角色和跑腿员角色特有的通知需求。
 * 由于跑腿员和用户使用同一登录角色，所以合并在一个文件中处理。
 *
 * 主要功能：
 * 1. 订单状态通知（用户）
 * 2. 支付相关通知（用户）
 * 3. 退款结果通知（用户）
 * 4. 配送进度通知（用户）
 * 5. 跑腿员任务通知（跑腿员）
 * 6. 跑腿员收益通知（跑腿员）
 * 7. 评价和投诉通知（用户/跑腿员）
 */

package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"o_mall_backend/modules/chat/dto"

	"github.com/beego/beego/v2/core/logs"
)

// WebSocketUserService 用户/跑腿员WebSocket通知服务接口
type WebSocketUserService interface {
	// 用户订单相关通知
	SendOrderStatusUpdateNotification(ctx context.Context, userID int64, orderID int64, orderNo string, oldStatus string, newStatus string, statusMessage string) error
	SendOrderPaymentSuccessNotification(ctx context.Context, userID int64, orderID int64, orderNo string, payAmount float64) error
	SendOrderDeliveryNotification(ctx context.Context, userID int64, orderID int64, orderNo string, deliveryStatus string, estimatedTime string) error
	SendOrderCompletedNotification(ctx context.Context, userID int64, orderID int64, orderNo string, totalAmount float64) error

	// 用户退款相关通知
	SendRefundResultNotification(ctx context.Context, userID int64, refundID int64, refundNo string, orderID int64, orderNo string, refundAmount float64, status string, remark string) error
	SendRefundProgressNotification(ctx context.Context, userID int64, refundID int64, refundNo string, orderID int64, orderNo string, progress string, estimatedTime string) error

	// 用户优惠券和促销通知
	SendCouponReceivedNotification(ctx context.Context, userID int64, couponID int64, couponName string, discountAmount float64, expireTime time.Time) error
	SendCouponExpireReminderNotification(ctx context.Context, userID int64, couponID int64, couponName string, expireTime time.Time) error
	SendPromotionNotification(ctx context.Context, userID int64, promotionID int64, promotionTitle string, promotionContent string) error

	// 跑腿员任务相关通知
	SendRunnerTaskAssignedNotification(ctx context.Context, runnerID int64, taskID int64, taskNo string, taskType string, pickupAddress string, deliveryAddress string, reward float64) error
	SendRunnerTaskStatusUpdateNotification(ctx context.Context, runnerID int64, taskID int64, taskNo string, oldStatus string, newStatus string) error
	SendRunnerTaskCancelledNotification(ctx context.Context, runnerID int64, taskID int64, taskNo string, cancelReason string) error

	// 跑腿员收益相关通知
	SendRunnerEarningsNotification(ctx context.Context, runnerID int64, taskID int64, taskNo string, earnings float64, bonusAmount float64) error
	SendRunnerWithdrawalNotification(ctx context.Context, runnerID int64, withdrawalID int64, amount float64, status string) error
	SendRunnerDailyEarningsNotification(ctx context.Context, runnerID int64, date time.Time, totalEarnings float64, taskCount int) error

	// 跑腿员状态相关通知
	SendRunnerStatusChangeNotification(ctx context.Context, runnerID int64, oldStatus string, newStatus string, reason string) error
	SendRunnerLocationUpdateNotification(ctx context.Context, runnerID int64, latitude float64, longitude float64, address string) error

	// 评价和投诉通知
	SendReviewReminderNotification(ctx context.Context, userID int64, orderID int64, orderNo string) error
	SendReviewReplyNotification(ctx context.Context, userID int64, reviewID int64, orderID int64, replyContent string) error
	SendComplaintStatusNotification(ctx context.Context, userID int64, complaintID int64, status string, handleResult string) error

	// 系统通知
	SendSystemNotificationToUser(ctx context.Context, userID int64, title string, content string, notificationType string) error
	SendAccountSecurityNotification(ctx context.Context, userID int64, securityType string, description string, ipAddress string) error
	SendBalanceChangeNotification(ctx context.Context, userID int64, changeType string, amount float64, balance float64, description string) error
}

// webSocketUserService 用户/跑腿员WebSocket通知服务实现
type webSocketUserService struct {
	commonService       WebSocketCommonService
	wsManager           WebSocketManager
	asyncMessageService AsyncMessageService
}

// NewWebSocketUserService 创建用户/跑腿员WebSocket通知服务
func NewWebSocketUserService() WebSocketUserService {
	container := GetServiceContainer()
	return &webSocketUserService{
		commonService:       NewWebSocketCommonService(),
		wsManager:           container.GetWebSocketManager(),
		asyncMessageService: container.GetAsyncMessageService(),
	}
}

// SendOrderStatusUpdateNotification 发送订单状态更新通知
func (s *webSocketUserService) SendOrderStatusUpdateNotification(ctx context.Context, userID int64, orderID int64, orderNo string, oldStatus string, newStatus string, statusMessage string) error {
	logs.Info("[用户WebSocket服务] 发送订单状态更新通知 - 用户ID: %d, 订单ID: %d, 状态: %s -> %s", userID, orderID, oldStatus, newStatus)

	data := map[string]interface{}{
		"order_id":          orderID,
		"order_no":          orderNo,
		"old_status":        oldStatus,
		"new_status":        newStatus,
		"status_message":    statusMessage,
		"notification_type": "order_status_update",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "order_detail",
		"action_url":        fmt.Sprintf("/user/takeout/order/%d", orderID),
		"message":           statusMessage,
	}

	return s.sendToUser(ctx, userID, "user_order_status_update", data)
}

// SendOrderPaymentSuccessNotification 发送订单支付成功通知
func (s *webSocketUserService) SendOrderPaymentSuccessNotification(ctx context.Context, userID int64, orderID int64, orderNo string, payAmount float64) error {
	logs.Info("[用户WebSocket服务] 发送订单支付成功通知 - 用户ID: %d, 订单ID: %d, 金额: %.2f", userID, orderID, payAmount)

	data := map[string]interface{}{
		"order_id":          orderID,
		"order_no":          orderNo,
		"pay_amount":        payAmount,
		"notification_type": "order_payment_success",
		"priority":          3, // 高优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "order_detail",
		"action_url":        fmt.Sprintf("/user/takeout/order/%d", orderID),
		"message":           fmt.Sprintf("您的订单 %s 支付成功，支付金额 %.2f 元", orderNo, payAmount),
	}

	return s.sendToUser(ctx, userID, "user_order_payment_success", data)
}

// SendOrderDeliveryNotification 发送订单配送通知
func (s *webSocketUserService) SendOrderDeliveryNotification(ctx context.Context, userID int64, orderID int64, orderNo string, deliveryStatus string, estimatedTime string) error {
	logs.Info("[用户WebSocket服务] 发送订单配送通知 - 用户ID: %d, 订单ID: %d, 配送状态: %s", userID, orderID, deliveryStatus)

	data := map[string]interface{}{
		"order_id":          orderID,
		"order_no":          orderNo,
		"delivery_status":   deliveryStatus,
		"estimated_time":    estimatedTime,
		"notification_type": "order_delivery_update",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "order_tracking",
		"action_url":        fmt.Sprintf("/order/tracking/%d", orderID),
		"message":           fmt.Sprintf("您的订单 %s 配送状态更新：%s", orderNo, deliveryStatus),
	}

	return s.sendToUser(ctx, userID, "user_order_delivery_update", data)
}

// SendOrderCompletedNotification 发送订单完成通知
func (s *webSocketUserService) SendOrderCompletedNotification(ctx context.Context, userID int64, orderID int64, orderNo string, totalAmount float64) error {
	logs.Info("[用户WebSocket服务] 发送订单完成通知 - 用户ID: %d, 订单ID: %d", userID, orderID)

	data := map[string]interface{}{
		"order_id":          orderID,
		"order_no":          orderNo,
		"total_amount":      totalAmount,
		"notification_type": "order_completed",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "order_review",
		"action_url":        fmt.Sprintf("/order/review/%d", orderID),
		"message":           fmt.Sprintf("您的订单 %s 已完成，欢迎评价", orderNo),
	}

	return s.sendToUser(ctx, userID, "user_order_completed", data)
}

// SendRefundResultNotification 发送退款结果通知
func (s *webSocketUserService) SendRefundResultNotification(ctx context.Context, userID int64, refundID int64, refundNo string, orderID int64, orderNo string, refundAmount float64, status string, remark string) error {
	logs.Info("[用户WebSocket服务] 发送退款结果通知 - 用户ID: %d, 退款ID: %d, 状态: %s", userID, refundID, status)

	var message string
	var priority int
	switch status {
	case "approved":
		message = fmt.Sprintf("您的退款申请已通过，退款金额 %.2f 元将在3-5个工作日内退回", refundAmount)
		priority = 3 // 高优先级
	case "rejected":
		message = fmt.Sprintf("很抱歉，您的退款申请被拒绝。拒绝原因：%s", remark)
		priority = 3 // 高优先级
	case "processing":
		message = fmt.Sprintf("您的退款申请正在处理中，退款金额 %.2f 元", refundAmount)
		priority = 2 // 中优先级
	default:
		message = fmt.Sprintf("您的退款状态已更新：%s", status)
		priority = 2 // 中优先级
	}

	data := map[string]interface{}{
		"refund_id":         refundID,
		"refund_no":         refundNo,
		"order_id":          orderID,
		"order_no":          orderNo,
		"refund_amount":     refundAmount,
		"status":            status,
		"remark":            remark,
		"notification_type": "refund_result",
		"priority":          priority,
		"timestamp":         time.Now().Unix(),
		"action_type":       "refund_detail",
		"action_url":        fmt.Sprintf("/user/takeout/order/%d", orderID), // 修复：使用orderID而不是refundID
		"message":           message,
	}

	return s.sendToUser(ctx, userID, "user_refund_result", data)
}

// SendRefundProgressNotification 发送退款进度通知
func (s *webSocketUserService) SendRefundProgressNotification(ctx context.Context, userID int64, refundID int64, refundNo string, orderID int64, orderNo string, progress string, estimatedTime string) error {
	logs.Info("[用户WebSocket服务] 发送退款进度通知 - 用户ID: %d, 退款ID: %d, 订单ID: %d, 进度: %s", userID, refundID, orderID, progress)

	data := map[string]interface{}{
		"refund_id":         refundID,
		"refund_no":         refundNo,
		"order_id":          orderID,
		"order_no":          orderNo,
		"progress":          progress,
		"estimated_time":    estimatedTime,
		"notification_type": "refund_progress",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "refund_detail",
		"action_url":        fmt.Sprintf("/user/takeout/order/%d", orderID), // 修复：使用orderID而不是refundID
		"message":           fmt.Sprintf("退款进度更新：%s", progress),
	}

	return s.sendToUser(ctx, userID, "user_refund_progress", data)
}

// SendCouponReceivedNotification 发送优惠券领取通知
func (s *webSocketUserService) SendCouponReceivedNotification(ctx context.Context, userID int64, couponID int64, couponName string, discountAmount float64, expireTime time.Time) error {
	logs.Info("[用户WebSocket服务] 发送优惠券领取通知 - 用户ID: %d, 优惠券ID: %d", userID, couponID)

	data := map[string]interface{}{
		"coupon_id":         couponID,
		"coupon_name":       couponName,
		"discount_amount":   discountAmount,
		"expire_time":       expireTime.Format("2006-01-02 15:04:05"),
		"notification_type": "coupon_received",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "coupon_detail",
		"action_url":        fmt.Sprintf("/coupon/detail/%d", couponID),
		"message":           fmt.Sprintf("您获得了优惠券：%s，优惠金额 %.2f 元", couponName, discountAmount),
	}

	return s.sendToUser(ctx, userID, "user_coupon_received", data)
}

// SendCouponExpireReminderNotification 发送优惠券过期提醒通知
func (s *webSocketUserService) SendCouponExpireReminderNotification(ctx context.Context, userID int64, couponID int64, couponName string, expireTime time.Time) error {
	logs.Info("[用户WebSocket服务] 发送优惠券过期提醒通知 - 用户ID: %d, 优惠券ID: %d", userID, couponID)

	data := map[string]interface{}{
		"coupon_id":         couponID,
		"coupon_name":       couponName,
		"expire_time":       expireTime.Format("2006-01-02 15:04:05"),
		"notification_type": "coupon_expire_reminder",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "coupon_use",
		"action_url":        fmt.Sprintf("/coupon/use/%d", couponID),
		"message":           fmt.Sprintf("您的优惠券 %s 即将过期，过期时间：%s", couponName, expireTime.Format("2006-01-02 15:04")),
	}

	return s.sendToUser(ctx, userID, "user_coupon_expire_reminder", data)
}

// SendPromotionNotification 发送促销活动通知
func (s *webSocketUserService) SendPromotionNotification(ctx context.Context, userID int64, promotionID int64, promotionTitle string, promotionContent string) error {
	logs.Info("[用户WebSocket服务] 发送促销活动通知 - 用户ID: %d, 活动ID: %d", userID, promotionID)

	data := map[string]interface{}{
		"promotion_id":      promotionID,
		"promotion_title":   promotionTitle,
		"promotion_content": promotionContent,
		"notification_type": "promotion",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "promotion_detail",
		"action_url":        fmt.Sprintf("/promotion/detail/%d", promotionID),
		"message":           promotionContent,
	}

	return s.sendToUser(ctx, userID, "user_promotion", data)
}

// SendRunnerTaskAssignedNotification 发送跑腿员任务分配通知
func (s *webSocketUserService) SendRunnerTaskAssignedNotification(ctx context.Context, runnerID int64, taskID int64, taskNo string, taskType string, pickupAddress string, deliveryAddress string, reward float64) error {
	logs.Info("[用户WebSocket服务] 发送跑腿员任务分配通知 - 跑腿员ID: %d, 任务ID: %d", runnerID, taskID)

	data := map[string]interface{}{
		"task_id":           taskID,
		"task_no":           taskNo,
		"task_type":         taskType,
		"pickup_address":    pickupAddress,
		"delivery_address":  deliveryAddress,
		"reward":            reward,
		"notification_type": "runner_task_assigned",
		"priority":          3, // 高优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "task_detail",
		"action_url":        fmt.Sprintf("/runner/task/detail/%d", taskID),
		"message":           fmt.Sprintf("您有新的任务：%s，报酬 %.2f 元", taskType, reward),
	}

	return s.sendToUser(ctx, runnerID, "runner_task_assigned", data)
}

// SendRunnerTaskStatusUpdateNotification 发送跑腿员任务状态更新通知
func (s *webSocketUserService) SendRunnerTaskStatusUpdateNotification(ctx context.Context, runnerID int64, taskID int64, taskNo string, oldStatus string, newStatus string) error {
	logs.Info("[用户WebSocket服务] 发送跑腿员任务状态更新通知 - 跑腿员ID: %d, 任务ID: %d, 状态: %s -> %s", runnerID, taskID, oldStatus, newStatus)

	data := map[string]interface{}{
		"task_id":           taskID,
		"task_no":           taskNo,
		"old_status":        oldStatus,
		"new_status":        newStatus,
		"notification_type": "runner_task_status_update",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "task_detail",
		"action_url":        fmt.Sprintf("/runner/task/detail/%d", taskID),
		"message":           fmt.Sprintf("任务 %s 状态更新：%s -> %s", taskNo, oldStatus, newStatus),
	}

	return s.sendToUser(ctx, runnerID, "runner_task_status_update", data)
}

// SendRunnerTaskCancelledNotification 发送跑腿员任务取消通知
func (s *webSocketUserService) SendRunnerTaskCancelledNotification(ctx context.Context, runnerID int64, taskID int64, taskNo string, cancelReason string) error {
	logs.Info("[用户WebSocket服务] 发送跑腿员任务取消通知 - 跑腿员ID: %d, 任务ID: %d", runnerID, taskID)

	data := map[string]interface{}{
		"task_id":           taskID,
		"task_no":           taskNo,
		"cancel_reason":     cancelReason,
		"notification_type": "runner_task_cancelled",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "task_list",
		"action_url":        "/runner/task/list",
		"message":           fmt.Sprintf("任务 %s 已取消，取消原因：%s", taskNo, cancelReason),
	}

	return s.sendToUser(ctx, runnerID, "runner_task_cancelled", data)
}

// SendRunnerEarningsNotification 发送跑腿员收益通知
func (s *webSocketUserService) SendRunnerEarningsNotification(ctx context.Context, runnerID int64, taskID int64, taskNo string, earnings float64, bonusAmount float64) error {
	logs.Info("[用户WebSocket服务] 发送跑腿员收益通知 - 跑腿员ID: %d, 任务ID: %d, 收益: %.2f", runnerID, taskID, earnings)

	totalEarnings := earnings + bonusAmount
	data := map[string]interface{}{
		"task_id":           taskID,
		"task_no":           taskNo,
		"earnings":          earnings,
		"bonus_amount":      bonusAmount,
		"total_earnings":    totalEarnings,
		"notification_type": "runner_earnings",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "earnings_detail",
		"action_url":        fmt.Sprintf("/runner/earnings/detail/%d", taskID),
		"message":           fmt.Sprintf("任务 %s 完成，获得收益 %.2f 元", taskNo, totalEarnings),
	}

	return s.sendToUser(ctx, runnerID, "runner_earnings", data)
}

// SendRunnerWithdrawalNotification 发送跑腿员提现通知
func (s *webSocketUserService) SendRunnerWithdrawalNotification(ctx context.Context, runnerID int64, withdrawalID int64, amount float64, status string) error {
	logs.Info("[用户WebSocket服务] 发送跑腿员提现通知 - 跑腿员ID: %d, 提现ID: %d, 状态: %s", runnerID, withdrawalID, status)

	data := map[string]interface{}{
		"withdrawal_id":     withdrawalID,
		"amount":            amount,
		"status":            status,
		"notification_type": "runner_withdrawal",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "withdrawal_detail",
		"action_url":        fmt.Sprintf("/runner/withdrawal/detail/%d", withdrawalID),
		"message":           fmt.Sprintf("提现申请状态更新：%s，金额：%.2f元", status, amount),
	}

	return s.sendToUser(ctx, runnerID, "runner_withdrawal", data)
}

// SendRunnerDailyEarningsNotification 发送跑腿员日收益通知
func (s *webSocketUserService) SendRunnerDailyEarningsNotification(ctx context.Context, runnerID int64, date time.Time, totalEarnings float64, taskCount int) error {
	logs.Info("[用户WebSocket服务] 发送跑腿员日收益通知 - 跑腿员ID: %d, 日期: %s, 收益: %.2f", runnerID, date.Format("2006-01-02"), totalEarnings)

	data := map[string]interface{}{
		"date":              date.Format("2006-01-02"),
		"total_earnings":    totalEarnings,
		"task_count":        taskCount,
		"notification_type": "runner_daily_earnings",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "earnings_report",
		"action_url":        fmt.Sprintf("/runner/earnings/daily/%s", date.Format("2006-01-02")),
		"message":           fmt.Sprintf("今日收益统计：完成 %d 个任务，总收益 %.2f 元", taskCount, totalEarnings),
	}

	return s.sendToUser(ctx, runnerID, "runner_daily_earnings", data)
}

// SendRunnerStatusChangeNotification 发送跑腿员状态变更通知
func (s *webSocketUserService) SendRunnerStatusChangeNotification(ctx context.Context, runnerID int64, oldStatus string, newStatus string, reason string) error {
	logs.Info("[用户WebSocket服务] 发送跑腿员状态变更通知 - 跑腿员ID: %d, 状态: %s -> %s", runnerID, oldStatus, newStatus)

	data := map[string]interface{}{
		"old_status":        oldStatus,
		"new_status":        newStatus,
		"reason":            reason,
		"notification_type": "runner_status_change",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "runner_profile",
		"action_url":        "/runner/profile",
		"message":           fmt.Sprintf("您的状态已更新：%s -> %s，原因：%s", oldStatus, newStatus, reason),
	}

	return s.sendToUser(ctx, runnerID, "runner_status_change", data)
}

// SendRunnerLocationUpdateNotification 发送跑腿员位置更新通知
func (s *webSocketUserService) SendRunnerLocationUpdateNotification(ctx context.Context, runnerID int64, latitude float64, longitude float64, address string) error {
	logs.Info("[用户WebSocket服务] 发送跑腿员位置更新通知 - 跑腿员ID: %d, 地址: %s", runnerID, address)

	data := map[string]interface{}{
		"latitude":          latitude,
		"longitude":         longitude,
		"address":           address,
		"notification_type": "runner_location_update",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "location_update",
		"message":           fmt.Sprintf("位置已更新：%s", address),
	}

	return s.sendToUser(ctx, runnerID, "runner_location_update", data)
}

// SendReviewReminderNotification 发送评价提醒通知
func (s *webSocketUserService) SendReviewReminderNotification(ctx context.Context, userID int64, orderID int64, orderNo string) error {
	logs.Info("[用户WebSocket服务] 发送评价提醒通知 - 用户ID: %d, 订单ID: %d", userID, orderID)

	data := map[string]interface{}{
		"order_id":          orderID,
		"order_no":          orderNo,
		"notification_type": "review_reminder",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "order_review",
		"action_url":        fmt.Sprintf("/order/review/%d", orderID),
		"message":           fmt.Sprintf("您的订单 %s 已完成，快来评价吧", orderNo),
	}

	return s.sendToUser(ctx, userID, "user_review_reminder", data)
}

// SendReviewReplyNotification 发送评价回复通知
func (s *webSocketUserService) SendReviewReplyNotification(ctx context.Context, userID int64, reviewID int64, orderID int64, replyContent string) error {
	logs.Info("[用户WebSocket服务] 发送评价回复通知 - 用户ID: %d, 评价ID: %d", userID, reviewID)

	data := map[string]interface{}{
		"review_id":         reviewID,
		"order_id":          orderID,
		"reply_content":     replyContent,
		"notification_type": "review_reply",
		"priority":          1, // 低优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "review_detail",
		"action_url":        fmt.Sprintf("/review/detail/%d", reviewID),
		"message":           "商家回复了您的评价",
	}

	return s.sendToUser(ctx, userID, "user_review_reply", data)
}

// SendComplaintStatusNotification 发送投诉状态通知
func (s *webSocketUserService) SendComplaintStatusNotification(ctx context.Context, userID int64, complaintID int64, status string, handleResult string) error {
	logs.Info("[用户WebSocket服务] 发送投诉状态通知 - 用户ID: %d, 投诉ID: %d, 状态: %s", userID, complaintID, status)

	data := map[string]interface{}{
		"complaint_id":      complaintID,
		"status":            status,
		"handle_result":     handleResult,
		"notification_type": "complaint_status",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "complaint_detail",
		"action_url":        fmt.Sprintf("/complaint/detail/%d", complaintID),
		"message":           fmt.Sprintf("您的投诉状态已更新：%s", status),
	}

	return s.sendToUser(ctx, userID, "user_complaint_status", data)
}

// SendSystemNotificationToUser 发送系统通知给用户
func (s *webSocketUserService) SendSystemNotificationToUser(ctx context.Context, userID int64, title string, content string, notificationType string) error {
	logs.Info("[用户WebSocket服务] 发送系统通知 - 用户ID: %d, 类型: %s", userID, notificationType)

	data := map[string]interface{}{
		"title":             title,
		"content":           content,
		"notification_type": notificationType,
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "system_notification",
		"message":           content,
	}

	return s.sendToUser(ctx, userID, "user_system_notification", data)
}

// SendAccountSecurityNotification 发送账户安全通知
func (s *webSocketUserService) SendAccountSecurityNotification(ctx context.Context, userID int64, securityType string, description string, ipAddress string) error {
	logs.Info("[用户WebSocket服务] 发送账户安全通知 - 用户ID: %d, 类型: %s", userID, securityType)

	data := map[string]interface{}{
		"security_type":     securityType,
		"description":       description,
		"ip_address":        ipAddress,
		"notification_type": "account_security",
		"priority":          3, // 高优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "security_center",
		"action_url":        "/user/security",
		"message":           fmt.Sprintf("账户安全提醒：%s", description),
	}

	return s.sendToUser(ctx, userID, "user_account_security", data)
}

// SendBalanceChangeNotification 发送余额变动通知
func (s *webSocketUserService) SendBalanceChangeNotification(ctx context.Context, userID int64, changeType string, amount float64, balance float64, description string) error {
	logs.Info("[用户WebSocket服务] 发送余额变动通知 - 用户ID: %d, 类型: %s, 金额: %.2f", userID, changeType, amount)

	data := map[string]interface{}{
		"change_type":       changeType,
		"amount":            amount,
		"balance":           balance,
		"description":       description,
		"notification_type": "balance_change",
		"priority":          2, // 中优先级
		"timestamp":         time.Now().Unix(),
		"action_type":       "balance_detail",
		"action_url":        "/user/balance",
		"message":           fmt.Sprintf("余额变动：%s %.2f 元，当前余额：%.2f 元", changeType, amount, balance),
	}

	return s.sendToUser(ctx, userID, "user_balance_change", data)
}

// sendToUser 发送消息给用户
func (s *webSocketUserService) sendToUser(ctx context.Context, userID int64, event string, data map[string]interface{}) error {
	// 检查WebSocket管理器是否已初始化
	if s.wsManager == nil {
		logs.Warn("[用户WebSocket服务] WebSocket管理器未初始化，跳过消息发送")
		return fmt.Errorf("WebSocket管理器未初始化")
	}

	wsMessage := &dto.WebSocketMessageDTO{
		Type:      "notification",
		Event:     event,
		Timestamp: time.Now().Unix(),
		Data:      data,
	}

	err := s.wsManager.SendToAllUserDevices(userID, "user", wsMessage)
	if err != nil {
		logs.Error("[用户WebSocket服务] 发送通知失败 - 用户ID: %d, 事件: %s, 错误: %v", userID, event, err)
		return err
	}

	// 异步保存通知消息到数据库
	if s.asyncMessageService != nil {
		// 从data中提取内容作为消息内容
		content := ""
		if data != nil {
			if title, ok := data["title"].(string); ok {
				content = title
			} else if msg, ok := data["message"].(string); ok {
				content = msg
			} else if content_val, ok := data["content"].(string); ok {
				content = content_val
			}
		}

		// 判断是否为订单相关通知，需要关联到订单通知会话
		var sessionID int64 = 0 // 默认为系统通知，不关联会话
		if data != nil {
			if notificationType, ok := data["notification_type"].(string); ok {
				// 订单相关通知需要关联到订单通知会话
				// 包括：订单通知(order_*)、退款通知(refund_*)
				if strings.HasPrefix(notificationType, "order_") || strings.HasPrefix(notificationType, "refund_") {
					// 获取或创建订单通知会话
					if orderSessionID, err := s.getOrCreateOrderNotificationSession(ctx, userID, "user"); err == nil {
						sessionID = orderSessionID
						logs.Info("[用户WebSocket服务] 订单通知关联到会话 - 用户ID: %d, 会话ID: %d, 通知类型: %s", userID, sessionID, notificationType)
					} else {
						logs.Error("[用户WebSocket服务] 获取订单通知会话失败: %v, 用户ID: %d", err, userID)
					}
				}
			}
		}

		task := &AsyncMessageTask{
			MessageType: "notification",
			SenderID:    0, // 系统通知发送者ID为0
			SenderType:  "system",
			TargetID:    userID,
			TargetType:  "user",
			Content:     content,
			WSMessage:   wsMessage,
			NotifyData:  data,
			SessionID:   sessionID, // 设置会话ID
		}

		// 异步保存，不阻塞当前流程
		go func() {
			if err := s.asyncMessageService.SaveNotificationMessageAsync(ctx, task); err != nil {
				logs.Error("[用户WebSocket服务] 异步保存通知消息失败: %v", err)
			}
		}()
	}

	logs.Info("[用户WebSocket服务] 通知发送成功 - 用户ID: %d, 事件: %s", userID, event)
	return nil
}

// getOrCreateOrderNotificationSession 获取或创建订单通知会话
func (s *webSocketUserService) getOrCreateOrderNotificationSession(ctx context.Context, userID int64, userType string) (int64, error) {
	// 通过公共服务获取聊天服务
	container := GetServiceContainer()
	chatService := container.GetChatService()
	if chatService == nil {
		return 0, fmt.Errorf("聊天服务未初始化")
	}

	// 查找或创建订单通知会话
	session, err := chatService.FindOrCreateSession(ctx, 0, "system", userID, userType)
	if err != nil {
		return 0, fmt.Errorf("获取订单通知会话失败: %v", err)
	}

	return session.ID, nil
}
