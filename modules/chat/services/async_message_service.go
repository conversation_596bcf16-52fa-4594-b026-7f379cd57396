/**
 * async_message_service.go
 * 异步消息处理服务
 *
 * 该文件提供异步消息保存功能，确保WebSocket通知发送后能够异步保存到数据库，
 * 不阻塞原来的API进程。包括消息队列、批量处理、错误重试等功能。
 */

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"
	"o_mall_backend/modules/chat/repositories"
	"o_mall_backend/modules/chat/repositories/impl"

	"github.com/beego/beego/v2/core/logs"
)

// AsyncMessageTask 异步消息任务
type AsyncMessageTask struct {
	ID          string                   `json:"id"`           // 任务ID
	MessageType string                   `json:"message_type"` // 消息类型
	SenderID    int64                    `json:"sender_id"`    // 发送者ID
	SenderType  string                   `json:"sender_type"`  // 发送者类型
	TargetID    int64                    `json:"target_id"`    // 接收者ID
	TargetType  string                   `json:"target_type"`  // 接收者类型
	SessionID   int64                    `json:"session_id"`   // 会话ID（0表示不关联会话）
	Content     string                   `json:"content"`      // 消息内容
	WSMessage   *dto.WebSocketMessageDTO `json:"ws_message"`   // WebSocket消息
	NotifyData  map[string]interface{}   `json:"notify_data"`  // 通知数据
	CreatedAt   time.Time                `json:"created_at"`   // 创建时间
	RetryCount  int                      `json:"retry_count"`  // 重试次数
	MaxRetries  int                      `json:"max_retries"`  // 最大重试次数
}

// AsyncMessageService 异步消息服务接口
type AsyncMessageService interface {
	// 启动异步消息处理服务
	Start() error

	// 停止异步消息处理服务
	Stop() error

	// 异步保存WebSocket通知消息
	SaveNotificationMessageAsync(ctx context.Context, task *AsyncMessageTask) error

	// 异步保存文本消息
	SaveTextMessageAsync(ctx context.Context, task *AsyncMessageTask) error

	// 异步保存系统消息
	SaveSystemMessageAsync(ctx context.Context, task *AsyncMessageTask) error

	// 获取队列状态
	GetQueueStatus() map[string]interface{}
}

// asyncMessageService 异步消息服务实现
type asyncMessageService struct {
	chatRepo     repositories.ChatRepository // 聊天仓库
	taskQueue    chan *AsyncMessageTask      // 任务队列
	workerPool   chan struct{}               // 工作池
	stopChan     chan struct{}               // 停止信号
	wg           sync.WaitGroup              // 等待组
	mutex        sync.RWMutex                // 读写锁
	isRunning    bool                        // 运行状态
	maxWorkers   int                         // 最大工作协程数
	queueSize    int                         // 队列大小
	batchSize    int                         // 批处理大小
	batchTimeout time.Duration               // 批处理超时时间
}

// NewAsyncMessageService 创建异步消息服务
func NewAsyncMessageService() AsyncMessageService {
	return &asyncMessageService{
		chatRepo:     impl.NewChatRepository(),
		taskQueue:    make(chan *AsyncMessageTask, 1000), // 队列大小1000
		workerPool:   make(chan struct{}, 10),            // 最多10个工作协程
		stopChan:     make(chan struct{}),
		maxWorkers:   10,
		queueSize:    1000,
		batchSize:    50,              // 批处理50条消息
		batchTimeout: 5 * time.Second, // 5秒超时
		isRunning:    false,
	}
}

// Start 启动异步消息处理服务
func (s *asyncMessageService) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("异步消息服务已经在运行中")
	}

	logs.Info("[异步消息服务] 启动异步消息处理服务")

	// 启动工作协程
	for i := 0; i < s.maxWorkers; i++ {
		s.wg.Add(1)
		go s.worker(i)
	}

	s.isRunning = true
	logs.Info("[异步消息服务] 异步消息处理服务已启动，工作协程数: %d", s.maxWorkers)
	return nil
}

// Stop 停止异步消息处理服务
func (s *asyncMessageService) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return fmt.Errorf("异步消息服务未在运行")
	}

	logs.Info("[异步消息服务] 停止异步消息处理服务")

	// 发送停止信号
	close(s.stopChan)

	// 等待所有工作协程完成
	s.wg.Wait()

	s.isRunning = false
	logs.Info("[异步消息服务] 异步消息处理服务已停止")
	return nil
}

// SaveNotificationMessageAsync 异步保存WebSocket通知消息
func (s *asyncMessageService) SaveNotificationMessageAsync(ctx context.Context, task *AsyncMessageTask) error {
	if task == nil {
		return fmt.Errorf("任务不能为空")
	}

	// 设置任务默认值
	if task.ID == "" {
		task.ID = fmt.Sprintf("notify_%d_%d_%d", task.SenderID, task.TargetID, time.Now().UnixNano())
	}
	if task.CreatedAt.IsZero() {
		task.CreatedAt = time.Now()
	}
	if task.MaxRetries == 0 {
		task.MaxRetries = 3
	}

	// 非阻塞发送到队列
	select {
	case s.taskQueue <- task:
		logs.Debug("[异步消息服务] 通知消息任务已加入队列: %s", task.ID)
		return nil
	default:
		logs.Error("[异步消息服务] 任务队列已满，丢弃通知消息任务: %s", task.ID)
		return fmt.Errorf("任务队列已满")
	}
}

// SaveTextMessageAsync 异步保存文本消息
func (s *asyncMessageService) SaveTextMessageAsync(ctx context.Context, task *AsyncMessageTask) error {
	if task == nil {
		return fmt.Errorf("任务不能为空")
	}

	// 设置任务默认值
	if task.ID == "" {
		task.ID = fmt.Sprintf("text_%d_%d_%d", task.SenderID, task.TargetID, time.Now().UnixNano())
	}
	if task.CreatedAt.IsZero() {
		task.CreatedAt = time.Now()
	}
	if task.MaxRetries == 0 {
		task.MaxRetries = 3
	}

	// 非阻塞发送到队列
	select {
	case s.taskQueue <- task:
		logs.Debug("[异步消息服务] 文本消息任务已加入队列: %s", task.ID)
		return nil
	default:
		logs.Error("[异步消息服务] 任务队列已满，丢弃文本消息任务: %s", task.ID)
		return fmt.Errorf("任务队列已满")
	}
}

// SaveSystemMessageAsync 异步保存系统消息
func (s *asyncMessageService) SaveSystemMessageAsync(ctx context.Context, task *AsyncMessageTask) error {
	if task == nil {
		return fmt.Errorf("任务不能为空")
	}

	// 设置任务默认值
	if task.ID == "" {
		task.ID = fmt.Sprintf("system_%d_%d", task.TargetID, time.Now().UnixNano())
	}
	if task.CreatedAt.IsZero() {
		task.CreatedAt = time.Now()
	}
	if task.MaxRetries == 0 {
		task.MaxRetries = 3
	}

	// 非阻塞发送到队列
	select {
	case s.taskQueue <- task:
		logs.Debug("[异步消息服务] 系统消息任务已加入队列: %s", task.ID)
		return nil
	default:
		logs.Error("[异步消息服务] 任务队列已满，丢弃系统消息任务: %s", task.ID)
		return fmt.Errorf("任务队列已满")
	}
}

// GetQueueStatus 获取队列状态
func (s *asyncMessageService) GetQueueStatus() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	return map[string]interface{}{
		"is_running":     s.isRunning,
		"queue_length":   len(s.taskQueue),
		"queue_capacity": s.queueSize,
		"max_workers":    s.maxWorkers,
		"batch_size":     s.batchSize,
		"batch_timeout":  s.batchTimeout.String(),
	}
}

// worker 工作协程
func (s *asyncMessageService) worker(workerID int) {
	defer s.wg.Done()

	logs.Info("[异步消息服务] 工作协程 %d 已启动", workerID)

	for {
		select {
		case <-s.stopChan:
			logs.Info("[异步消息服务] 工作协程 %d 收到停止信号", workerID)
			return

		case task := <-s.taskQueue:
			if task != nil {
				s.processTask(workerID, task)
			}
		}
	}
}

// processTask 处理任务
func (s *asyncMessageService) processTask(workerID int, task *AsyncMessageTask) {
	logs.Debug("[异步消息服务] 工作协程 %d 开始处理任务: %s", workerID, task.ID)

	ctx := context.Background()
	var err error

	switch task.MessageType {
	case "notification":
		err = s.saveNotificationMessage(ctx, task)
	case "text":
		err = s.saveTextMessage(ctx, task)
	case "system":
		err = s.saveSystemMessage(ctx, task)
	default:
		logs.Error("[异步消息服务] 未知的消息类型: %s, 任务ID: %s", task.MessageType, task.ID)
		return
	}

	if err != nil {
		logs.Error("[异步消息服务] 工作协程 %d 处理任务失败: %s, 错误: %v", workerID, task.ID, err)

		// 重试逻辑
		if task.RetryCount < task.MaxRetries {
			task.RetryCount++
			logs.Info("[异步消息服务] 任务 %s 将进行第 %d 次重试", task.ID, task.RetryCount)

			// 延迟重试
			go func() {
				time.Sleep(time.Duration(task.RetryCount) * time.Second)
				select {
				case s.taskQueue <- task:
					logs.Debug("[异步消息服务] 任务 %s 重试已加入队列", task.ID)
				default:
					logs.Error("[异步消息服务] 任务 %s 重试时队列已满", task.ID)
				}
			}()
		} else {
			logs.Error("[异步消息服务] 任务 %s 已达到最大重试次数，放弃处理", task.ID)
		}
	} else {
		logs.Debug("[异步消息服务] 工作协程 %d 成功处理任务: %s", workerID, task.ID)
	}
}

// saveNotificationMessage 保存通知消息
func (s *asyncMessageService) saveNotificationMessage(ctx context.Context, task *AsyncMessageTask) error {
	logs.Debug("[异步消息服务] 保存通知消息: %s", task.ID)

	// 序列化通知数据
	var notificationData string
	if task.NotifyData != nil {
		dataBytes, err := json.Marshal(task.NotifyData)
		if err != nil {
			logs.Error("[异步消息服务] 序列化通知数据失败: %v", err)
			return err
		}
		notificationData = string(dataBytes)
	}

	// 从WebSocket消息中提取通知类型
	notificationType := "general"
	if task.WSMessage != nil && task.WSMessage.Event != "" {
		notificationType = task.WSMessage.Event
	}

	// 创建消息记录
	message := &models.ChatMessage{
		SessionID:        task.SessionID, // 使用任务中的会话ID
		SenderID:         task.SenderID,
		SenderType:       task.SenderType,
		Content:          task.Content,
		Type:             models.MessageTypeNotification,
		Status:           0, // 未读状态
		NotificationType: notificationType,
		NotificationData: notificationData,
		Priority:         1, // 默认优先级
		Persistent:       true,
	}

	// 保存到数据库
	messageID, err := s.chatRepo.CreateMessage(ctx, message)
	if err != nil {
		logs.Error("[异步消息服务] 保存通知消息失败: %v", err)
		return err
	}

	// 如果消息关联到会话，清除相关缓存确保前端能获取到最新数据
	if task.SessionID > 0 {
		logs.Info("[异步消息服务] 通知消息已关联到会话 - 会话ID: %d, 用户ID: %d", task.SessionID, task.TargetID)
		// 清除相关缓存，确保前端能获取到最新数据
		s.clearRelatedCache(task.TargetID, task.TargetType)
	}

	logs.Info("[异步消息服务] 通知消息保存成功 - ID: %d, 会话ID: %d, 任务: %s", messageID, task.SessionID, task.ID)
	return nil
}

// saveTextMessage 保存文本消息
func (s *asyncMessageService) saveTextMessage(ctx context.Context, task *AsyncMessageTask) error {
	logs.Debug("[异步消息服务] 保存文本消息: %s", task.ID)

	// 创建消息记录
	message := &models.ChatMessage{
		SessionID:  0, // 如果需要会话ID，可以从task中获取
		SenderID:   task.SenderID,
		SenderType: task.SenderType,
		Content:    task.Content,
		Type:       models.MessageTypeText,
		Status:     0, // 未读状态
	}

	// 保存到数据库
	messageID, err := s.chatRepo.CreateMessage(ctx, message)
	if err != nil {
		logs.Error("[异步消息服务] 保存文本消息失败: %v", err)
		return err
	}

	logs.Info("[异步消息服务] 文本消息保存成功 - ID: %d, 任务: %s", messageID, task.ID)
	return nil
}

// saveSystemMessage 保存系统消息
func (s *asyncMessageService) saveSystemMessage(ctx context.Context, task *AsyncMessageTask) error {
	logs.Debug("[异步消息服务] 保存系统消息: %s", task.ID)

	// 序列化系统消息数据
	var notificationData string
	if task.NotifyData != nil {
		dataBytes, err := json.Marshal(task.NotifyData)
		if err != nil {
			logs.Error("[异步消息服务] 序列化系统消息数据失败: %v", err)
			return err
		}
		notificationData = string(dataBytes)
	}

	// 创建系统消息记录
	message := &models.ChatMessage{
		SessionID:        0, // 系统消息不关联会话
		SenderID:         0, // 系统消息发送者ID为0
		SenderType:       "system",
		Content:          task.Content,
		Type:             models.MessageTypeNotification,
		Status:           0, // 未读状态
		NotificationType: "system_message",
		NotificationData: notificationData,
		Priority:         2, // 系统消息优先级较高
		Persistent:       true,
	}

	// 保存到数据库
	messageID, err := s.chatRepo.CreateMessage(ctx, message)
	if err != nil {
		logs.Error("[异步消息服务] 保存系统消息失败: %v", err)
		return err
	}

	logs.Info("[异步消息服务] 系统消息保存成功 - ID: %d, 任务: %s", messageID, task.ID)
	return nil
}

// clearRelatedCache 清除相关缓存
func (s *asyncMessageService) clearRelatedCache(userID int64, userType string) {
	// 获取缓存服务
	container := GetServiceContainer()
	cacheService := container.GetCacheService()
	if cacheService == nil {
		logs.Warn("[异步消息服务] 缓存服务未初始化，跳过缓存清理")
		return
	}

	// 清除用户未读消息数量缓存
	err := cacheService.DeleteUnreadCount(userID, userType)
	if err != nil {
		logs.Error("[异步消息服务] 清除未读消息数量缓存失败: %v, 用户ID: %d, 类型: %s", err, userID, userType)
	} else {
		logs.Info("[异步消息服务] 清除未读消息数量缓存成功 - 用户ID: %d, 类型: %s", userID, userType)
	}

	// 清除消息分类缓存
	err = cacheService.DeleteMessageCategories(userID, userType)
	if err != nil {
		logs.Error("[异步消息服务] 清除消息分类缓存失败: %v, 用户ID: %d, 类型: %s", err, userID, userType)
	} else {
		logs.Info("[异步消息服务] 清除消息分类缓存成功 - 用户ID: %d, 类型: %s", userID, userType)
	}
}
