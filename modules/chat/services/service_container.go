/**
 * service_container.go
 * 服务容器用于存储全局服务实例
 *
 * 该文件定义了一个服务容器，用于在应用程序生命周期内保存服务实例，
 * 确保控制器可以在每次请求过程中获取相同的服务实例
 */

package services

import (
	"sync"
)

// ServiceContainer 全局服务容器
type ServiceContainer struct {
	chatService             ChatService
	wsManager               WebSocketManager
	notificationService     NotificationService
	messageCategoryService  MessageCategoryService
	pushNotificationService PushNotificationService
	cacheService            CacheService
	friendService           *FriendService
	groupService            *GroupService
	groupMemberService      *GroupMemberService
	blacklistService        *BlacklistService
	// 新的WebSocket通知服务
	wsCommonService   WebSocketCommonService
	wsAdminService    WebSocketAdminService
	wsMerchantService WebSocketMerchantService
	wsUserService     WebSocketUserService
	// 异步消息服务
	asyncMessageService AsyncMessageService
	mutex               sync.RWMutex
}

// 全局服务容器实例
var container *ServiceContainer
var once sync.Once

// GetServiceContainer 获取单例服务容器
func GetServiceContainer() *ServiceContainer {
	once.Do(func() {
		container = &ServiceContainer{}
	})
	return container
}

// SetChatService 设置聊天服务
func (c *ServiceContainer) SetChatService(service ChatService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.chatService = service
}

// GetChatService 获取聊天服务
func (c *ServiceContainer) GetChatService() ChatService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.chatService
}

// SetWebSocketManager 设置WebSocket管理器
func (c *ServiceContainer) SetWebSocketManager(manager WebSocketManager) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.wsManager = manager
}

// GetWebSocketManager 获取WebSocket管理器
func (c *ServiceContainer) GetWebSocketManager() WebSocketManager {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.wsManager
}

// SetFriendService 设置好友服务
func (c *ServiceContainer) SetFriendService(service *FriendService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.friendService = service
}

// GetFriendService 获取好友服务
func (c *ServiceContainer) GetFriendService() *FriendService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.friendService
}

// SetGroupService 设置群组服务
func (c *ServiceContainer) SetGroupService(service *GroupService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.groupService = service
}

// GetGroupService 获取群组服务
func (c *ServiceContainer) GetGroupService() *GroupService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.groupService
}

// SetGroupMemberService 设置群组成员服务
func (c *ServiceContainer) SetGroupMemberService(service *GroupMemberService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.groupMemberService = service
}

// GetGroupMemberService 获取群组成员服务
func (c *ServiceContainer) GetGroupMemberService() *GroupMemberService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.groupMemberService
}

// SetBlacklistService 设置黑名单服务
func (c *ServiceContainer) SetBlacklistService(service *BlacklistService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.blacklistService = service
}

// GetBlacklistService 获取黑名单服务
func (c *ServiceContainer) GetBlacklistService() *BlacklistService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.blacklistService
}

// SetNotificationService 设置通知服务
func (c *ServiceContainer) SetNotificationService(service NotificationService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.notificationService = service
}

// GetNotificationService 获取通知服务
func (c *ServiceContainer) GetNotificationService() NotificationService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.notificationService
}

// SetMessageCategoryService 设置消息分类服务
func (c *ServiceContainer) SetMessageCategoryService(service MessageCategoryService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.messageCategoryService = service
}

// GetMessageCategoryService 获取消息分类服务
func (c *ServiceContainer) GetMessageCategoryService() MessageCategoryService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.messageCategoryService
}

// SetPushNotificationService 设置推送通知服务
func (c *ServiceContainer) SetPushNotificationService(service PushNotificationService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.pushNotificationService = service
}

// GetPushNotificationService 获取推送通知服务
func (c *ServiceContainer) GetPushNotificationService() PushNotificationService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.pushNotificationService
}

// SetCacheService 设置缓存服务
func (c *ServiceContainer) SetCacheService(service CacheService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.cacheService = service
}

// GetCacheService 获取缓存服务
func (c *ServiceContainer) GetCacheService() CacheService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.cacheService
}

// SetWebSocketCommonService 设置WebSocket通用服务
func (c *ServiceContainer) SetWebSocketCommonService(service WebSocketCommonService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.wsCommonService = service
}

// GetWebSocketCommonService 获取WebSocket通用服务
func (c *ServiceContainer) GetWebSocketCommonService() WebSocketCommonService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	if c.wsCommonService == nil {
		c.wsCommonService = NewWebSocketCommonService()
	}
	return c.wsCommonService
}

// SetWebSocketAdminService 设置WebSocket管理员服务
func (c *ServiceContainer) SetWebSocketAdminService(service WebSocketAdminService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.wsAdminService = service
}

// GetWebSocketAdminService 获取WebSocket管理员服务
func (c *ServiceContainer) GetWebSocketAdminService() WebSocketAdminService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	if c.wsAdminService == nil {
		c.wsAdminService = NewWebSocketAdminService()
	}
	return c.wsAdminService
}

// SetWebSocketMerchantService 设置WebSocket商家服务
func (c *ServiceContainer) SetWebSocketMerchantService(service WebSocketMerchantService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.wsMerchantService = service
}

// GetWebSocketMerchantService 获取WebSocket商家服务
func (c *ServiceContainer) GetWebSocketMerchantService() WebSocketMerchantService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	if c.wsMerchantService == nil {
		c.wsMerchantService = NewWebSocketMerchantService()
	}
	return c.wsMerchantService
}

// SetWebSocketUserService 设置WebSocket用户服务
func (c *ServiceContainer) SetWebSocketUserService(service WebSocketUserService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.wsUserService = service
}

// GetWebSocketUserService 获取WebSocket用户服务
func (c *ServiceContainer) GetWebSocketUserService() WebSocketUserService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	if c.wsUserService == nil {
		c.wsUserService = NewWebSocketUserService()
	}
	return c.wsUserService
}

// SetAsyncMessageService 设置异步消息服务
func (c *ServiceContainer) SetAsyncMessageService(service AsyncMessageService) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.asyncMessageService = service
}

// GetAsyncMessageService 获取异步消息服务
func (c *ServiceContainer) GetAsyncMessageService() AsyncMessageService {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	if c.asyncMessageService == nil {
		c.asyncMessageService = NewAsyncMessageService()
	}
	return c.asyncMessageService
}
