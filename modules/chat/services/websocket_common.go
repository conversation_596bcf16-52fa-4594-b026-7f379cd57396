/**
 * websocket_common.go
 * WebSocket公共通知功能
 *
 * 本文件实现了WebSocket通知的公共功能，包括发送文本消息、媒体消息等基础通知功能。
 * 为管理员、商家、用户三种角色的专用通知提供统一的底层支持。
 *
 * 主要功能：
 * 1. 文本消息发送
 * 2. 媒体消息发送（图片、文件、语音等）
 * 3. 系统消息发送
 * 4. 消息格式化和验证
 * 5. 多设备推送支持
 */

package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"o_mall_backend/modules/chat/dto"
	"o_mall_backend/modules/chat/models"

	"github.com/beego/beego/v2/core/logs"
)

// WebSocketCommonService WebSocket公共通知服务接口
type WebSocketCommonService interface {
	// 基础消息发送
	SendTextMessage(ctx context.Context, senderID int64, senderType string, targetID int64, targetType string, content string) error
	SendMediaMessage(ctx context.Context, senderID int64, senderType string, targetID int64, targetType string, mediaType string, resourceID string, fileName string) error
	SendSystemMessage(ctx context.Context, targetID int64, targetType string, title string, content string, data map[string]interface{}) error

	// 批量消息发送
	BroadcastTextMessage(ctx context.Context, senderID int64, senderType string, targets []NotificationTarget, content string) error
	BroadcastSystemMessage(ctx context.Context, targets []NotificationTarget, title string, content string, data map[string]interface{}) error

	// 聊天消息广播
	BroadcastChatMessage(ctx context.Context, message *models.ChatMessage) error

	// 消息格式化
	FormatNotificationMessage(notificationType NotificationType, data map[string]interface{}) (*dto.WebSocketMessageDTO, error)
	FormatChatMessage(message *models.ChatMessage) (*dto.WebSocketMessageDTO, error)

	// 连接状态检查
	IsUserOnline(userID int64, userType string) bool
	GetUserDeviceCount(userID int64, userType string) int

	// 消息验证
	ValidateMessageContent(content string) error
	ValidateMediaResource(resourceID string, mediaType string) error
}

// webSocketCommonService WebSocket公共通知服务实现
type webSocketCommonService struct {
	wsManager           WebSocketManager
	notificationService NotificationService
	chatService         ChatService
	asyncMessageService AsyncMessageService
}

// NewWebSocketCommonService 创建WebSocket公共通知服务
func NewWebSocketCommonService() WebSocketCommonService {
	container := GetServiceContainer()
	return &webSocketCommonService{
		wsManager:           container.GetWebSocketManager(),
		notificationService: container.GetNotificationService(),
		chatService:         container.GetChatService(),
		asyncMessageService: container.GetAsyncMessageService(),
	}
}

// SendTextMessage 发送文本消息
func (s *webSocketCommonService) SendTextMessage(ctx context.Context, senderID int64, senderType string, targetID int64, targetType string, content string) error {
	logs.Info("[WebSocket公共服务] 发送文本消息 - 发送者: %d(%s), 接收者: %d(%s)", senderID, senderType, targetID, targetType)

	// 检查WebSocket管理器是否已初始化
	if s.wsManager == nil {
		logs.Warn("[WebSocket公共服务] WebSocket管理器未初始化，跳过消息发送")
		return fmt.Errorf("WebSocket管理器未初始化")
	}

	// 验证消息内容
	if err := s.ValidateMessageContent(content); err != nil {
		return fmt.Errorf("消息内容验证失败: %v", err)
	}

	// 构建WebSocket消息
	wsMessage := &dto.WebSocketMessageDTO{
		Type:      "message",
		Event:     "text_message",
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"sender_id":    senderID,
			"sender_type":  senderType,
			"target_id":    targetID,
			"target_type":  targetType,
			"content":      content,
			"message_type": "text",
		},
	}

	// 发送给目标用户的所有设备
	err := s.wsManager.SendToAllUserDevices(targetID, targetType, wsMessage)
	if err != nil {
		logs.Error("[WebSocket公共服务] 发送文本消息失败: %v", err)
		return err
	}

	// 异步保存消息到数据库
	if s.asyncMessageService != nil {
		task := &AsyncMessageTask{
			MessageType: "text",
			SenderID:    senderID,
			SenderType:  senderType,
			TargetID:    targetID,
			TargetType:  targetType,
			SessionID:   0, // 直接发送的消息不关联会话
			Content:     content,
			WSMessage:   wsMessage,
		}

		// 异步保存，不阻塞当前流程
		go func() {
			if err := s.asyncMessageService.SaveTextMessageAsync(ctx, task); err != nil {
				logs.Error("[WebSocket公共服务] 异步保存文本消息失败: %v", err)
			}
		}()
	}

	logs.Info("[WebSocket公共服务] 文本消息发送成功")
	return nil
}

// SendMediaMessage 发送媒体消息
func (s *webSocketCommonService) SendMediaMessage(ctx context.Context, senderID int64, senderType string, targetID int64, targetType string, mediaType string, resourceID string, fileName string) error {
	logs.Info("[WebSocket公共服务] 发送媒体消息 - 发送者: %d(%s), 接收者: %d(%s), 媒体类型: %s", senderID, senderType, targetID, targetType, mediaType)

	// 验证媒体资源
	if err := s.ValidateMediaResource(resourceID, mediaType); err != nil {
		return fmt.Errorf("媒体资源验证失败: %v", err)
	}

	// 构建WebSocket消息
	wsMessage := &dto.WebSocketMessageDTO{
		Type:      "message",
		Event:     "media_message",
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"sender_id":    senderID,
			"sender_type":  senderType,
			"target_id":    targetID,
			"target_type":  targetType,
			"message_type": mediaType,
			"resource_id":  resourceID,
			"file_name":    fileName,
		},
	}

	// 发送给目标用户的所有设备
	err := s.wsManager.SendToAllUserDevices(targetID, targetType, wsMessage)
	if err != nil {
		logs.Error("[WebSocket公共服务] 发送媒体消息失败: %v", err)
		return err
	}

	logs.Info("[WebSocket公共服务] 媒体消息发送成功")
	return nil
}

// SendSystemMessage 发送系统消息
func (s *webSocketCommonService) SendSystemMessage(ctx context.Context, targetID int64, targetType string, title string, content string, data map[string]interface{}) error {
	logs.Info("[WebSocket公共服务] 发送系统消息 - 接收者: %d(%s), 标题: %s", targetID, targetType, title)

	// 检查WebSocket管理器是否已初始化
	if s.wsManager == nil {
		logs.Warn("[WebSocket公共服务] WebSocket管理器未初始化，跳过系统消息发送")
		return fmt.Errorf("WebSocket管理器未初始化")
	}

	if data == nil {
		data = make(map[string]interface{})
	}

	// 添加系统消息标识
	data["title"] = title
	data["content"] = content
	data["system_message"] = true

	// 构建WebSocket消息
	wsMessage := &dto.WebSocketMessageDTO{
		Type:      "notification",
		Event:     "system_message",
		Timestamp: time.Now().Unix(),
		Data:      data,
	}

	// 发送给目标用户的所有设备
	err := s.wsManager.SendToAllUserDevices(targetID, targetType, wsMessage)
	if err != nil {
		logs.Error("[WebSocket公共服务] 发送系统消息失败: %v", err)
		return err
	}

	// 异步保存系统消息到数据库
	if s.asyncMessageService != nil {
		task := &AsyncMessageTask{
			MessageType: "system",
			SenderID:    0, // 系统消息发送者ID为0
			SenderType:  "system",
			TargetID:    targetID,
			TargetType:  targetType,
			SessionID:   0, // 系统消息不关联会话
			Content:     content,
			WSMessage:   wsMessage,
			NotifyData:  data,
		}

		// 异步保存，不阻塞当前流程
		go func() {
			if err := s.asyncMessageService.SaveSystemMessageAsync(ctx, task); err != nil {
				logs.Error("[WebSocket公共服务] 异步保存系统消息失败: %v", err)
			}
		}()
	}

	logs.Info("[WebSocket公共服务] 系统消息发送成功")
	return nil
}

// BroadcastTextMessage 广播文本消息
func (s *webSocketCommonService) BroadcastTextMessage(ctx context.Context, senderID int64, senderType string, targets []NotificationTarget, content string) error {
	logs.Info("[WebSocket公共服务] 广播文本消息 - 发送者: %d(%s), 目标数量: %d", senderID, senderType, len(targets))

	// 验证消息内容
	if err := s.ValidateMessageContent(content); err != nil {
		return fmt.Errorf("消息内容验证失败: %v", err)
	}

	successCount := 0
	for _, target := range targets {
		err := s.SendTextMessage(ctx, senderID, senderType, target.UserID, target.UserType, content)
		if err != nil {
			logs.Error("[WebSocket公共服务] 广播文本消息失败 - 目标: %d(%s), 错误: %v", target.UserID, target.UserType, err)
		} else {
			successCount++
		}
	}

	logs.Info("[WebSocket公共服务] 文本消息广播完成 - 成功: %d/%d", successCount, len(targets))
	return nil
}

// BroadcastSystemMessage 广播系统消息
func (s *webSocketCommonService) BroadcastSystemMessage(ctx context.Context, targets []NotificationTarget, title string, content string, data map[string]interface{}) error {
	logs.Info("[WebSocket公共服务] 广播系统消息 - 标题: %s, 目标数量: %d", title, len(targets))

	successCount := 0
	for _, target := range targets {
		err := s.SendSystemMessage(ctx, target.UserID, target.UserType, title, content, data)
		if err != nil {
			logs.Error("[WebSocket公共服务] 广播系统消息失败 - 目标: %d(%s), 错误: %v", target.UserID, target.UserType, err)
		} else {
			successCount++
		}
	}

	logs.Info("[WebSocket公共服务] 系统消息广播完成 - 成功: %d/%d", successCount, len(targets))
	return nil
}

// BroadcastChatMessage 广播聊天消息到会话
func (s *webSocketCommonService) BroadcastChatMessage(ctx context.Context, message *models.ChatMessage) error {
	logs.Info("[WebSocket公共服务] 广播聊天消息 - 会话ID: %d, 发送者: %d(%s)", message.SessionID, message.SenderID, message.SenderType)

	// 检查WebSocket管理器是否已初始化
	if s.wsManager == nil {
		logs.Warn("[WebSocket公共服务] WebSocket管理器未初始化，跳过消息广播")
		return fmt.Errorf("WebSocket管理器未初始化")
	}

	// 使用FormatChatMessage格式化消息
	wsMessage, err := s.FormatChatMessage(message)
	if err != nil {
		logs.Error("[WebSocket公共服务] 格式化聊天消息失败: %v", err)
		return fmt.Errorf("格式化聊天消息失败: %v", err)
	}

	// 广播消息到会话中的所有用户
	err = s.wsManager.BroadcastToSession(message.SessionID, wsMessage)
	if err != nil {
		logs.Error("[WebSocket公共服务] 广播聊天消息失败: %v", err)
		return err
	}

	// 异步保存消息到数据库
	if s.asyncMessageService != nil {
		task := &AsyncMessageTask{
			MessageType: "text", // 使用现有的text类型
			SenderID:    message.SenderID,
			SenderType:  message.SenderType,
			TargetID:    0, // 聊天消息不需要特定目标，会话中的所有用户都是目标
			TargetType:  "",
			SessionID:   message.SessionID, // 聊天消息关联到会话
			Content:     message.Content,
			WSMessage:   wsMessage,
		}

		// 异步保存，不阻塞当前流程
		go func() {
			if err := s.asyncMessageService.SaveTextMessageAsync(ctx, task); err != nil {
				logs.Error("[WebSocket公共服务] 异步保存聊天消息失败: %v", err)
			}
		}()
	}

	logs.Info("[WebSocket公共服务] 聊天消息广播成功")
	return nil
}

// FormatNotificationMessage 格式化通知消息
func (s *webSocketCommonService) FormatNotificationMessage(notificationType NotificationType, data map[string]interface{}) (*dto.WebSocketMessageDTO, error) {
	if data == nil {
		data = make(map[string]interface{})
	}

	// 添加通知类型
	data["notification_type"] = string(notificationType)

	return &dto.WebSocketMessageDTO{
		Type:      "notification",
		Event:     string(notificationType),
		Timestamp: time.Now().Unix(),
		Data:      data,
	}, nil
}

// FormatChatMessage 格式化聊天消息
func (s *webSocketCommonService) FormatChatMessage(message *models.ChatMessage) (*dto.WebSocketMessageDTO, error) {
	if message == nil {
		return nil, fmt.Errorf("消息对象不能为空")
	}

	// 根据消息类型确定事件名称
	var eventName string
	switch message.Type {
	case models.MessageTypeText:
		eventName = "text_message"
	case models.MessageTypeImage:
		eventName = "media_message"
	case models.MessageTypeFile:
		eventName = "media_message"
	case models.MessageTypeVoice:
		eventName = "media_message"
	case models.MessageTypeVideo:
		eventName = "media_message"
	case models.MessageTypeNotification:
		eventName = "notification_message"
	default:
		eventName = "new_message" // 默认事件名称
	}

	// 构建完整的消息数据，包含发送者信息
	data := map[string]interface{}{
		"id":          message.ID,
		"session_id":  message.SessionID,
		"sender_id":   message.SenderID,
		"sender_type": message.SenderType,
		"content":     message.Content,
		"type":        message.Type,
		"resource_id": message.ResourceID,
		"status":      message.Status,
		"created_at":  message.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// 如果是媒体消息，添加媒体相关信息
	if message.Type == models.MessageTypeImage || message.Type == models.MessageTypeFile ||
		message.Type == models.MessageTypeVoice || message.Type == models.MessageTypeVideo {
		data["file_name"] = message.FileName
		data["file_size"] = message.FileSize
		data["file_type"] = message.FileType
		data["file_ext"] = message.FileExt
	}

	// 如果是通知消息，添加通知相关信息
	if message.Type == models.MessageTypeNotification {
		data["notification_type"] = message.NotificationType
		if message.NotificationData != "" {
			var notificationData map[string]interface{}
			if err := json.Unmarshal([]byte(message.NotificationData), &notificationData); err == nil {
				data["notification_data"] = notificationData
			}
		}
		data["priority"] = message.Priority
	}

	// 填充发送者信息（姓名、头像等）
	if s.chatService != nil {
		// 创建临时的MessageDTO来使用现有的填充逻辑
		tempDTO := &dto.MessageDTO{
			ID:         message.ID,
			SessionID:  message.SessionID,
			SenderID:   message.SenderID,
			SenderType: message.SenderType,
		}
		s.chatService.FillSenderInfo(context.Background(), tempDTO)

		// 将填充的信息添加到data中
		data["sender_name"] = tempDTO.SenderName
		data["sender_avatar"] = tempDTO.SenderAvatar
	}

	return &dto.WebSocketMessageDTO{
		Type:      "message",
		Event:     eventName,
		SessionID: message.SessionID,
		Timestamp: time.Now().Unix(),
		Data:      data,
	}, nil
}

// IsUserOnline 检查用户是否在线
func (s *webSocketCommonService) IsUserOnline(userID int64, userType string) bool {
	if s.wsManager == nil {
		return false
	}
	return s.wsManager.IsUserOnline(userID, userType)
}

// GetUserDeviceCount 获取用户设备连接数量
func (s *webSocketCommonService) GetUserDeviceCount(userID int64, userType string) int {
	if s.wsManager == nil {
		return 0
	}
	clients := s.wsManager.GetAllUserClients(userID, userType)
	return len(clients)
}

// ValidateMessageContent 验证消息内容
func (s *webSocketCommonService) ValidateMessageContent(content string) error {
	if content == "" {
		return fmt.Errorf("消息内容不能为空")
	}
	if len(content) > 5000 {
		return fmt.Errorf("消息内容过长，最大支持5000字符")
	}
	return nil
}

// ValidateMediaResource 验证媒体资源
func (s *webSocketCommonService) ValidateMediaResource(resourceID string, mediaType string) error {
	if resourceID == "" {
		return fmt.Errorf("媒体资源ID不能为空")
	}
	if mediaType == "" {
		return fmt.Errorf("媒体类型不能为空")
	}

	// 验证支持的媒体类型
	supportedTypes := []string{"image", "file", "voice", "video"}
	isSupported := false
	for _, supportedType := range supportedTypes {
		if mediaType == supportedType {
			isSupported = true
			break
		}
	}
	if !isSupported {
		return fmt.Errorf("不支持的媒体类型: %s", mediaType)
	}

	return nil
}
