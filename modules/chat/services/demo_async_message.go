/**
 * demo_async_message.go
 * 异步消息保存功能演示程序
 *
 * 该文件演示了WebSocket消息发送后异步保存到数据库的完整流程
 */

package services

import (
	"context"
	"fmt"
	"o_mall_backend/modules/chat/dto"
	"time"

	"github.com/beego/beego/v2/core/logs"
)

// DemoAsyncMessageSave 演示异步消息保存功能
func DemoAsyncMessageSave() {
	fmt.Println("=== 异步消息保存功能演示 ===")

	// 1. 创建服务容器并初始化异步消息服务
	fmt.Println("\n1. 初始化异步消息服务...")
	container := GetServiceContainer()
	asyncService := NewAsyncMessageService()
	container.SetAsyncMessageService(asyncService)

	// 2. 演示WebSocket服务发送消息并异步保存
	fmt.Println("\n2. 演示WebSocket通知发送和异步保存...")

	// 创建WebSocket服务实例
	userService := NewWebSocketUserService()
	merchantService := NewWebSocketMerchantService()
	adminService := NewWebSocketAdminService()
	commonService := NewWebSocketCommonService()

	ctx := context.Background()

	// 演示用户订单支付成功通知
	fmt.Println("\n2.1 用户订单支付成功通知:")
	fmt.Println("   - 发送WebSocket消息给用户")
	fmt.Println("   - 异步保存通知消息到数据库")
	err := userService.SendOrderPaymentSuccessNotification(ctx, 1001, 2001, "ORD20250129001", 99.99)
	if err != nil {
		fmt.Printf("   - 结果: %v (WebSocket管理器未初始化，这是预期的)\n", err)
	}

	// 演示商家新订单通知
	fmt.Println("\n2.2 商家新订单通知:")
	fmt.Println("   - 发送WebSocket消息给商家")
	fmt.Println("   - 异步保存通知消息到数据库")
	customerInfo := map[string]interface{}{
		"user_id":   1001,
		"user_name": "张三",
		"phone":     "13800138000",
	}
	err = merchantService.SendNewOrderNotification(ctx, 2001, 3001, "ORD20250129001", 99.99, customerInfo)
	if err != nil {
		fmt.Printf("   - 结果: %v (WebSocket管理器未初始化，这是预期的)\n", err)
	}

	// 演示管理员系统错误通知
	fmt.Println("\n2.3 管理员系统错误通知:")
	fmt.Println("   - 广播WebSocket消息给所有管理员")
	fmt.Println("   - 异步保存通知消息到数据库")
	adminIDs := []int64{1, 2, 3}
	errorDetails := map[string]interface{}{
		"error_code": "DB_CONNECTION_TIMEOUT",
		"severity":   "high",
		"module":     "database",
	}
	err = adminService.SendSystemErrorAlert(ctx, adminIDs, "数据库连接异常", "数据库连接超时，请检查网络连接", errorDetails)
	if err != nil {
		fmt.Printf("   - 结果: %v (WebSocket管理器未初始化，这是预期的)\n", err)
	}

	// 演示通用文本消息
	fmt.Println("\n2.4 通用文本消息:")
	fmt.Println("   - 发送WebSocket文本消息")
	fmt.Println("   - 异步保存文本消息到数据库")
	err = commonService.SendTextMessage(ctx, 1001, "user", 1002, "user", "你好，这是一条测试消息！")
	if err != nil {
		fmt.Printf("   - 结果: %v (WebSocket管理器未初始化，这是预期的)\n", err)
	}

	// 演示系统消息
	fmt.Println("\n2.5 系统消息:")
	fmt.Println("   - 发送WebSocket系统消息")
	fmt.Println("   - 异步保存系统消息到数据库")
	systemData := map[string]interface{}{
		"maintenance_time": "2025-01-29 02:00:00",
		"duration":         "2小时",
	}
	err = commonService.SendSystemMessage(ctx, 1001, "user", "系统维护通知", "系统将于今晚2点进行维护，预计2小时", systemData)
	if err != nil {
		fmt.Printf("   - 结果: %v (WebSocket管理器未初始化，这是预期的)\n", err)
	}

	// 3. 演示异步消息任务的创建和处理
	fmt.Println("\n3. 异步消息任务处理流程:")
	fmt.Println("   - 创建异步消息任务")
	fmt.Println("   - 任务进入队列等待处理")
	fmt.Println("   - 工作协程处理任务")
	fmt.Println("   - 保存消息到数据库")

	// 创建示例任务
	wsMessage := &dto.WebSocketMessageDTO{
		Type:      "notification",
		Event:     "demo_notification",
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"title":   "演示通知",
			"content": "这是一条演示通知消息",
			"type":    "info",
		},
	}

	task := &AsyncMessageTask{
		MessageType: "notification",
		SenderID:    0,
		SenderType:  "system",
		TargetID:    1001,
		TargetType:  "user",
		Content:     "这是一条演示通知消息",
		WSMessage:   wsMessage,
		NotifyData: map[string]interface{}{
			"title":   "演示通知",
			"content": "这是一条演示通知消息",
			"type":    "info",
		},
	}

	fmt.Printf("   - 任务类型: %s\n", task.MessageType)
	fmt.Printf("   - 发送者: %d (%s)\n", task.SenderID, task.SenderType)
	fmt.Printf("   - 接收者: %d (%s)\n", task.TargetID, task.TargetType)
	fmt.Printf("   - 消息内容: %s\n", task.Content)

	// 4. 总结异步消息保存的优势
	fmt.Println("\n4. 异步消息保存的优势:")
	fmt.Println("   ✓ 不阻塞API响应，提高系统性能")
	fmt.Println("   ✓ 使用工作协程池，控制并发数量")
	fmt.Println("   ✓ 队列缓冲，处理突发流量")
	fmt.Println("   ✓ 错误重试机制，提高可靠性")
	fmt.Println("   ✓ 批处理优化，减少数据库压力")
	fmt.Println("   ✓ 优雅关闭，确保消息不丢失")

	fmt.Println("\n=== 演示完成 ===")
}

// DemoWebSocketIntegration 演示WebSocket服务集成
func DemoWebSocketIntegration() {
	fmt.Println("=== WebSocket服务集成演示 ===")

	// 展示服务架构
	fmt.Println("\n服务架构:")
	fmt.Println("┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐")
	fmt.Println("│   API接口调用   │───▶│  WebSocket服务   │───▶│  异步消息服务   │")
	fmt.Println("└─────────────────┘    └──────────────────┘    └─────────────────┘")
	fmt.Println("                              │                         │")
	fmt.Println("                              ▼                         ▼")
	fmt.Println("                    ┌──────────────────┐    ┌─────────────────┐")
	fmt.Println("                    │  WebSocket连接   │    │   数据库保存    │")
	fmt.Println("                    └──────────────────┘    └─────────────────┘")

	fmt.Println("\n角色分工:")
	fmt.Println("• websocket_common.go  - 通用功能（文本消息、媒体消息、系统消息）")
	fmt.Println("• websocket_admin.go   - 管理员通知（系统异常、性能监控、业务统计）")
	fmt.Println("• websocket_merchant.go - 商家通知（新订单、退款、营业状态）")
	fmt.Println("• websocket_user.go    - 用户通知（订单状态、支付成功、配送更新）")
	fmt.Println("• async_message_service.go - 异步消息保存（队列处理、数据库存储）")

	fmt.Println("\n消息流程:")
	fmt.Println("1. API接口调用WebSocket服务发送通知")
	fmt.Println("2. WebSocket服务立即发送消息给客户端")
	fmt.Println("3. 同时创建异步任务保存消息到数据库")
	fmt.Println("4. 异步任务进入队列，由工作协程处理")
	fmt.Println("5. 工作协程将消息保存到数据库")
	fmt.Println("6. API接口立即返回，不等待数据库操作")

	fmt.Println("\n=== 集成演示完成 ===")
}

// RunDemo 运行演示程序
func RunDemo() {
	logs.Info("开始运行异步消息保存功能演示")

	DemoAsyncMessageSave()
	fmt.Println()
	DemoWebSocketIntegration()

	logs.Info("异步消息保存功能演示完成")
}
