/**
 * websocket_admin.go
 * 管理员WebSocket通知功能
 *
 * 本文件实现了管理员专用的WebSocket通知功能，包括系统通知、异常情况处理、
 * 平台监控告警等管理员角色特有的通知需求。
 *
 * 主要功能：
 * 1. 系统维护通知
 * 2. 异常情况告警
 * 3. 平台数据统计通知
 * 4. 用户举报处理通知
 * 5. 商家审核通知
 * 6. 订单异常处理通知
 */

package services

import (
	"context"
	"fmt"
	"time"

	"o_mall_backend/modules/chat/dto"

	"github.com/beego/beego/v2/core/logs"
)

// WebSocketAdminService 管理员WebSocket通知服务接口
type WebSocketAdminService interface {
	// 系统通知
	SendSystemMaintenanceNotification(ctx context.Context, adminIDs []int64, title string, content string, startTime time.Time, endTime time.Time) error
	SendSystemAnnouncementNotification(ctx context.Context, adminIDs []int64, title string, content string, priority int) error

	// 异常告警
	SendSystemErrorAlert(ctx context.Context, adminIDs []int64, errorType string, errorMessage string, errorDetails map[string]interface{}) error
	SendPerformanceAlert(ctx context.Context, adminIDs []int64, metricName string, currentValue float64, threshold float64) error
	SendSecurityAlert(ctx context.Context, adminIDs []int64, alertType string, description string, sourceIP string) error

	// 业务异常通知
	SendOrderExceptionNotification(ctx context.Context, adminIDs []int64, orderID int64, orderNo string, exceptionType string, description string) error
	SendPaymentExceptionNotification(ctx context.Context, adminIDs []int64, paymentID int64, paymentNo string, exceptionType string, amount float64) error
	SendRefundExceptionNotification(ctx context.Context, adminIDs []int64, refundID int64, refundNo string, exceptionType string, amount float64) error

	// 用户管理通知
	SendUserReportNotification(ctx context.Context, adminIDs []int64, reportID int64, reportType string, reporterID int64, targetID int64) error
	SendUserBanNotification(ctx context.Context, adminIDs []int64, userID int64, banReason string, banDuration time.Duration) error

	// 商家管理通知
	SendMerchantAuditNotification(ctx context.Context, adminIDs []int64, merchantID int64, auditType string, status string) error
	SendMerchantViolationNotification(ctx context.Context, adminIDs []int64, merchantID int64, violationType string, description string) error

	// 数据统计通知
	SendDailyReportNotification(ctx context.Context, adminIDs []int64, reportDate time.Time, reportData map[string]interface{}) error
	SendThresholdAlert(ctx context.Context, adminIDs []int64, metricName string, currentValue interface{}, threshold interface{}) error

	// 获取所有在线管理员
	GetOnlineAdmins() []int64
}

// webSocketAdminService 管理员WebSocket通知服务实现
type webSocketAdminService struct {
	commonService       WebSocketCommonService
	wsManager           WebSocketManager
	asyncMessageService AsyncMessageService
}

// NewWebSocketAdminService 创建管理员WebSocket通知服务
func NewWebSocketAdminService() WebSocketAdminService {
	container := GetServiceContainer()
	return &webSocketAdminService{
		commonService:       NewWebSocketCommonService(),
		wsManager:           container.GetWebSocketManager(),
		asyncMessageService: container.GetAsyncMessageService(),
	}
}

// SendSystemMaintenanceNotification 发送系统维护通知
func (s *webSocketAdminService) SendSystemMaintenanceNotification(ctx context.Context, adminIDs []int64, title string, content string, startTime time.Time, endTime time.Time) error {
	logs.Info("[管理员WebSocket服务] 发送系统维护通知 - 管理员数量: %d", len(adminIDs))

	data := map[string]interface{}{
		"title":       title,
		"content":     content,
		"start_time":  startTime.Format("2006-01-02 15:04:05"),
		"end_time":    endTime.Format("2006-01-02 15:04:05"),
		"duration":    int(endTime.Sub(startTime).Minutes()),
		"alert_type":  "system_maintenance",
		"priority":    3, // 高优先级
		"action_type": "system_maintenance",
	}

	return s.broadcastToAdmins(ctx, adminIDs, "system_maintenance", data)
}

// SendSystemAnnouncementNotification 发送系统公告通知
func (s *webSocketAdminService) SendSystemAnnouncementNotification(ctx context.Context, adminIDs []int64, title string, content string, priority int) error {
	logs.Info("[管理员WebSocket服务] 发送系统公告通知 - 管理员数量: %d, 优先级: %d", len(adminIDs), priority)

	data := map[string]interface{}{
		"title":       title,
		"content":     content,
		"priority":    priority,
		"alert_type":  "system_announcement",
		"action_type": "system_announcement",
	}

	return s.broadcastToAdmins(ctx, adminIDs, "system_announcement", data)
}

// SendSystemErrorAlert 发送系统错误告警
func (s *webSocketAdminService) SendSystemErrorAlert(ctx context.Context, adminIDs []int64, errorType string, errorMessage string, errorDetails map[string]interface{}) error {
	logs.Info("[管理员WebSocket服务] 发送系统错误告警 - 错误类型: %s", errorType)

	data := map[string]interface{}{
		"error_type":    errorType,
		"error_message": errorMessage,
		"error_details": errorDetails,
		"alert_type":    "system_error",
		"priority":      3, // 高优先级
		"timestamp":     time.Now().Unix(),
		"action_type":   "error_handling",
	}

	return s.broadcastToAdmins(ctx, adminIDs, "system_error_alert", data)
}

// SendPerformanceAlert 发送性能告警
func (s *webSocketAdminService) SendPerformanceAlert(ctx context.Context, adminIDs []int64, metricName string, currentValue float64, threshold float64) error {
	logs.Info("[管理员WebSocket服务] 发送性能告警 - 指标: %s, 当前值: %.2f, 阈值: %.2f", metricName, currentValue, threshold)

	data := map[string]interface{}{
		"metric_name":   metricName,
		"current_value": currentValue,
		"threshold":     threshold,
		"alert_type":    "performance_alert",
		"priority":      2, // 中优先级
		"timestamp":     time.Now().Unix(),
		"action_type":   "performance_monitoring",
	}

	return s.broadcastToAdmins(ctx, adminIDs, "performance_alert", data)
}

// SendSecurityAlert 发送安全告警
func (s *webSocketAdminService) SendSecurityAlert(ctx context.Context, adminIDs []int64, alertType string, description string, sourceIP string) error {
	logs.Info("[管理员WebSocket服务] 发送安全告警 - 类型: %s, 来源IP: %s", alertType, sourceIP)

	data := map[string]interface{}{
		"alert_type":    "security_alert",
		"security_type": alertType,
		"description":   description,
		"source_ip":     sourceIP,
		"priority":      3, // 高优先级
		"timestamp":     time.Now().Unix(),
		"action_type":   "security_handling",
	}

	return s.broadcastToAdmins(ctx, adminIDs, "security_alert", data)
}

// SendOrderExceptionNotification 发送订单异常通知
func (s *webSocketAdminService) SendOrderExceptionNotification(ctx context.Context, adminIDs []int64, orderID int64, orderNo string, exceptionType string, description string) error {
	logs.Info("[管理员WebSocket服务] 发送订单异常通知 - 订单ID: %d, 异常类型: %s", orderID, exceptionType)

	data := map[string]interface{}{
		"order_id":       orderID,
		"order_no":       orderNo,
		"exception_type": exceptionType,
		"description":    description,
		"alert_type":     "order_exception",
		"priority":       2, // 中优先级
		"timestamp":      time.Now().Unix(),
		"action_type":    "order_handling",
		"action_url":     fmt.Sprintf("/admin/order/detail/%d", orderID),
	}

	return s.broadcastToAdmins(ctx, adminIDs, "order_exception", data)
}

// SendPaymentExceptionNotification 发送支付异常通知
func (s *webSocketAdminService) SendPaymentExceptionNotification(ctx context.Context, adminIDs []int64, paymentID int64, paymentNo string, exceptionType string, amount float64) error {
	logs.Info("[管理员WebSocket服务] 发送支付异常通知 - 支付ID: %d, 异常类型: %s, 金额: %.2f", paymentID, exceptionType, amount)

	data := map[string]interface{}{
		"payment_id":     paymentID,
		"payment_no":     paymentNo,
		"exception_type": exceptionType,
		"amount":         amount,
		"alert_type":     "payment_exception",
		"priority":       3, // 高优先级
		"timestamp":      time.Now().Unix(),
		"action_type":    "payment_handling",
		"action_url":     fmt.Sprintf("/admin/payment/detail/%d", paymentID),
	}

	return s.broadcastToAdmins(ctx, adminIDs, "payment_exception", data)
}

// SendRefundExceptionNotification 发送退款异常通知
func (s *webSocketAdminService) SendRefundExceptionNotification(ctx context.Context, adminIDs []int64, refundID int64, refundNo string, exceptionType string, amount float64) error {
	logs.Info("[管理员WebSocket服务] 发送退款异常通知 - 退款ID: %d, 异常类型: %s, 金额: %.2f", refundID, exceptionType, amount)

	data := map[string]interface{}{
		"refund_id":      refundID,
		"refund_no":      refundNo,
		"exception_type": exceptionType,
		"amount":         amount,
		"alert_type":     "refund_exception",
		"priority":       3, // 高优先级
		"timestamp":      time.Now().Unix(),
		"action_type":    "refund_handling",
		"action_url":     fmt.Sprintf("/admin/refund/detail/%d", refundID),
	}

	return s.broadcastToAdmins(ctx, adminIDs, "refund_exception", data)
}

// SendUserReportNotification 发送用户举报通知
func (s *webSocketAdminService) SendUserReportNotification(ctx context.Context, adminIDs []int64, reportID int64, reportType string, reporterID int64, targetID int64) error {
	logs.Info("[管理员WebSocket服务] 发送用户举报通知 - 举报ID: %d, 类型: %s", reportID, reportType)

	data := map[string]interface{}{
		"report_id":   reportID,
		"report_type": reportType,
		"reporter_id": reporterID,
		"target_id":   targetID,
		"alert_type":  "user_report",
		"priority":    2, // 中优先级
		"timestamp":   time.Now().Unix(),
		"action_type": "report_handling",
		"action_url":  fmt.Sprintf("/admin/report/detail/%d", reportID),
	}

	return s.broadcastToAdmins(ctx, adminIDs, "user_report", data)
}

// SendUserBanNotification 发送用户封禁通知
func (s *webSocketAdminService) SendUserBanNotification(ctx context.Context, adminIDs []int64, userID int64, banReason string, banDuration time.Duration) error {
	logs.Info("[管理员WebSocket服务] 发送用户封禁通知 - 用户ID: %d, 封禁时长: %v", userID, banDuration)

	data := map[string]interface{}{
		"user_id":      userID,
		"ban_reason":   banReason,
		"ban_duration": int(banDuration.Hours()),
		"alert_type":   "user_ban",
		"priority":     2, // 中优先级
		"timestamp":    time.Now().Unix(),
		"action_type":  "user_management",
		"action_url":   fmt.Sprintf("/admin/user/detail/%d", userID),
	}

	return s.broadcastToAdmins(ctx, adminIDs, "user_ban", data)
}

// SendMerchantAuditNotification 发送商家审核通知
func (s *webSocketAdminService) SendMerchantAuditNotification(ctx context.Context, adminIDs []int64, merchantID int64, auditType string, status string) error {
	logs.Info("[管理员WebSocket服务] 发送商家审核通知 - 商家ID: %d, 审核类型: %s, 状态: %s", merchantID, auditType, status)

	data := map[string]interface{}{
		"merchant_id": merchantID,
		"audit_type":  auditType,
		"status":      status,
		"alert_type":  "merchant_audit",
		"priority":    2, // 中优先级
		"timestamp":   time.Now().Unix(),
		"action_type": "merchant_audit",
		"action_url":  fmt.Sprintf("/admin/merchant/detail/%d", merchantID),
	}

	return s.broadcastToAdmins(ctx, adminIDs, "merchant_audit", data)
}

// SendMerchantViolationNotification 发送商家违规通知
func (s *webSocketAdminService) SendMerchantViolationNotification(ctx context.Context, adminIDs []int64, merchantID int64, violationType string, description string) error {
	logs.Info("[管理员WebSocket服务] 发送商家违规通知 - 商家ID: %d, 违规类型: %s", merchantID, violationType)

	data := map[string]interface{}{
		"merchant_id":    merchantID,
		"violation_type": violationType,
		"description":    description,
		"alert_type":     "merchant_violation",
		"priority":       3, // 高优先级
		"timestamp":      time.Now().Unix(),
		"action_type":    "merchant_violation",
		"action_url":     fmt.Sprintf("/admin/merchant/detail/%d", merchantID),
	}

	return s.broadcastToAdmins(ctx, adminIDs, "merchant_violation", data)
}

// SendDailyReportNotification 发送日报通知
func (s *webSocketAdminService) SendDailyReportNotification(ctx context.Context, adminIDs []int64, reportDate time.Time, reportData map[string]interface{}) error {
	logs.Info("[管理员WebSocket服务] 发送日报通知 - 日期: %s", reportDate.Format("2006-01-02"))

	data := map[string]interface{}{
		"report_date": reportDate.Format("2006-01-02"),
		"report_data": reportData,
		"alert_type":  "daily_report",
		"priority":    1, // 低优先级
		"timestamp":   time.Now().Unix(),
		"action_type": "report_view",
		"action_url":  fmt.Sprintf("/admin/report/daily/%s", reportDate.Format("2006-01-02")),
	}

	return s.broadcastToAdmins(ctx, adminIDs, "daily_report", data)
}

// SendThresholdAlert 发送阈值告警
func (s *webSocketAdminService) SendThresholdAlert(ctx context.Context, adminIDs []int64, metricName string, currentValue interface{}, threshold interface{}) error {
	logs.Info("[管理员WebSocket服务] 发送阈值告警 - 指标: %s", metricName)

	data := map[string]interface{}{
		"metric_name":   metricName,
		"current_value": currentValue,
		"threshold":     threshold,
		"alert_type":    "threshold_alert",
		"priority":      2, // 中优先级
		"timestamp":     time.Now().Unix(),
		"action_type":   "threshold_monitoring",
	}

	return s.broadcastToAdmins(ctx, adminIDs, "threshold_alert", data)
}

// GetOnlineAdmins 获取所有在线管理员
func (s *webSocketAdminService) GetOnlineAdmins() []int64 {
	// TODO: 实现获取在线管理员逻辑
	// 这里需要根据实际的管理员管理系统来实现
	return []int64{}
}

// broadcastToAdmins 向管理员广播消息
func (s *webSocketAdminService) broadcastToAdmins(ctx context.Context, adminIDs []int64, event string, data map[string]interface{}) error {
	// 检查WebSocket管理器是否已初始化
	if s.wsManager == nil {
		logs.Warn("[管理员WebSocket服务] WebSocket管理器未初始化，跳过消息广播")
		return fmt.Errorf("WebSocket管理器未初始化")
	}

	wsMessage := &dto.WebSocketMessageDTO{
		Type:      "notification",
		Event:     event,
		Timestamp: time.Now().Unix(),
		Data:      data,
	}

	successCount := 0
	for _, adminID := range adminIDs {
		err := s.wsManager.SendToAllUserDevices(adminID, "admin", wsMessage)
		if err != nil {
			logs.Error("[管理员WebSocket服务] 发送通知失败 - 管理员ID: %d, 错误: %v", adminID, err)
		} else {
			successCount++

			// 异步保存通知消息到数据库
			if s.asyncMessageService != nil {
				// 从data中提取内容作为消息内容
				content := ""
				if data != nil {
					if title, ok := data["title"].(string); ok {
						content = title
					} else if msg, ok := data["message"].(string); ok {
						content = msg
					} else if content_val, ok := data["content"].(string); ok {
						content = content_val
					}
				}

				task := &AsyncMessageTask{
					MessageType: "notification",
					SenderID:    0, // 系统通知发送者ID为0
					SenderType:  "system",
					TargetID:    adminID,
					TargetType:  "admin",
					SessionID:   0, // 管理员通知不关联会话
					Content:     content,
					WSMessage:   wsMessage,
					NotifyData:  data,
				}

				// 异步保存，不阻塞当前流程
				go func(adminID int64, task *AsyncMessageTask) {
					if err := s.asyncMessageService.SaveNotificationMessageAsync(ctx, task); err != nil {
						logs.Error("[管理员WebSocket服务] 异步保存通知消息失败 - 管理员ID: %d, 错误: %v", adminID, err)
					}
				}(adminID, task)
			}
		}
	}

	logs.Info("[管理员WebSocket服务] 通知广播完成 - 事件: %s, 成功: %d/%d", event, successCount, len(adminIDs))
	return nil
}
