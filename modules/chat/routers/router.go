/**
 * router.go
 * 聊天模块路由配置
 *
 * 该文件负责注册和配置聊天模块的API路由
 */

package routers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/modules/chat/controllers"
	chatMiddlewares "o_mall_backend/modules/chat/middlewares"
	repoImpl "o_mall_backend/modules/chat/repositories/impl"
	"o_mall_backend/modules/chat/services"
	svcImpl "o_mall_backend/modules/chat/services/impl"
	userRepositories "o_mall_backend/modules/user/repositories"
	userServices "o_mall_backend/modules/user/services"
)

// Init 初始化聊天模块路由
func Init() {
	// 创建仓储实例
	chatRepo := repoImpl.NewChatRepository()

	// 创建用户模块依赖
	referralConfigRepo := userRepositories.NewReferralConfigRepository()
	referralConfigService := userServices.NewReferralConfigService(referralConfigRepo)

	// 创建WebSocket管理器和所有服务实例
	wsManager := services.NewWebSocketManager()
	chatService := svcImpl.NewChatService(chatRepo, wsManager, referralConfigService)
	notificationService := svcImpl.NewNotificationService(wsManager, chatRepo)
	cacheService := services.NewCacheService()
	messageCategoryService := services.NewMessageCategoryService()
	pushNotificationService := services.NewPushNotificationService(wsManager)
	// 这些服务已经返回具体类型指针，直接使用
	friendService := services.NewFriendService()
	groupService := services.NewGroupService()
	groupMemberService := services.NewGroupMemberService()
	blacklistService := services.NewBlacklistService()

	// 创建并启动异步消息服务
	asyncMessageService := services.NewAsyncMessageService()
	if err := asyncMessageService.Start(); err != nil {
		logs.Error("[Chat Router] 启动异步消息服务失败: %v", err)
	} else {
		logs.Info("[Chat Router] 异步消息服务已启动")
	}

	// 检查服务是否正确初始化
	if chatService == nil {
		logs.Error("[Chat Router] 初始化chatService失败")
		return
	}
	if messageCategoryService == nil {
		logs.Error("[Chat Router] 初始化messageCategoryService失败")
		return
	}

	// 将服务实例保存到全局服务容器
	container := services.GetServiceContainer()
	container.SetChatService(chatService)
	container.SetWebSocketManager(wsManager)
	container.SetNotificationService(notificationService)
	container.SetCacheService(cacheService)
	container.SetMessageCategoryService(messageCategoryService)
	container.SetPushNotificationService(pushNotificationService)
	container.SetFriendService(friendService)
	container.SetGroupService(groupService)
	container.SetGroupMemberService(groupMemberService)
	container.SetBlacklistService(blacklistService)
	container.SetAsyncMessageService(asyncMessageService)

	logs.Info("[Chat Router] 所有服务实例已保存到服务容器")

	// 获取WebSocket通知服务实例
	wsCommonService := container.GetWebSocketCommonService()
	if wsCommonService == nil {
		logs.Error("[Chat Router] 获取WebSocket公共服务失败")
		return
	}

	// 使用构造函数初始化控制器实例
	wsController := controllers.NewWebSocketController(wsManager)
	sessionController := controllers.NewSessionController(chatService)
	messageController := controllers.NewMessageController(chatService, wsCommonService)
	pushNotificationController := controllers.NewPushNotificationController(pushNotificationService)
	groupController := controllers.NewGroupController(groupService, groupMemberService)
	groupMemberController := controllers.NewGroupMemberController(groupMemberService)
	friendController := controllers.NewFriendController(friendService)
	blacklistController := controllers.NewBlacklistController(blacklistService)

	// 注册API路由
	ns := web.NewNamespace("/api/v1/chat",
		// 应用JWT中间件
		web.NSBefore(chatMiddlewares.JWTMiddleware()),

		// WebSocket连接
		web.NSRouter("/ws", wsController, "get:Connect"),

		// 会话相关路由
		web.NSRouter("/sessions", sessionController, "post:CreateSession;get:GetSessionList;options:Options"),
		web.NSRouter("/sessions/:id", sessionController, "get:GetSession;options:Options"),
		web.NSRouter("/sessions/:id/read", sessionController, "put:MarkAsRead;post:MarkAsRead;options:Options"),
		web.NSRouter("/sessions/:id/join", sessionController, "post:JoinSession;options:Options"),

		// 消息相关路由
		web.NSRouter("/sessions/:session_id/messages", messageController, "get:GetMessages;options:Options"),
		web.NSRouter("/sessions/:session_id/messages/text", messageController, "post:SendTextMessage;options:Options"),
		web.NSRouter("/sessions/:session_id/messages/media", messageController, "post:SendMediaMessage;options:Options"),
		web.NSRouter("/sessions/:session_id/messages/media-url", messageController, "post:SendMediaURLMessage;options:Options"),

		// 通知消息相关路由
		web.NSRouter("/notifications", messageController, "get:GetNotificationMessages;options:Options"),

		// 消息分类相关路由
		web.NSRouter("/message-categories", &controllers.MessageCategoryController{}, "get:GetMessageCategories;options:Options"),
		web.NSRouter("/unread-count", &controllers.MessageCategoryController{}, "get:GetUnreadCount;options:Options"),

		// 消息已读标记相关路由（更具体的路由放在前面）
		web.NSRouter("/messages/mark-read/:message_id", &controllers.MessageCategoryController{}, "post:MarkMessageAsRead;options:Options"),
		web.NSRouter("/messages/mark-category-read/:category", &controllers.MessageCategoryController{}, "post:MarkCategoryAsRead;options:Options"),
		web.NSRouter("/messages/mark-session-read/:session_id", &controllers.MessageCategoryController{}, "post:MarkSessionAsRead;options:Options"),

		// 通用消息查询路由（放在最后避免冲突）
		web.NSRouter("/messages", &controllers.MessageCategoryController{}, "get:GetMessages;options:Options"),

		// 推送通知相关路由
		web.NSRouter("/push/system-notification", pushNotificationController, "post:SendSystemNotification;options:Options"),
		web.NSRouter("/push/read-status/:message_id", pushNotificationController, "post:PushMessageReadStatus;options:Options"),
		web.NSRouter("/push/unread-update/:category", pushNotificationController, "post:PushCategoryUnreadUpdate;options:Options"),

		// 群聊相关路由
		web.NSRouter("/groups", groupController, "post:CreateGroup;get:GetUserGroups;options:Options"),
		web.NSRouter("/groups/:group_id", groupController, "get:GetGroupInfo;put:UpdateGroupInfo;options:Options"),
		web.NSRouter("/groups/:group_id/dismiss", groupController, "post:DismissGroup;options:Options"),

		// 群聊会话相关路由
		web.NSRouter("/groups/:group_id/join", sessionController, "post:JoinGroupSession;options:Options"),
		web.NSRouter("/groups/:group_id/leave", sessionController, "post:LeaveGroupSession;options:Options"),
		web.NSRouter("/groups/:group_id/session", sessionController, "get:GetGroupSessionDetails;options:Options"),

		// 群组成员管理路由
		web.NSRouter("/groups/:group_id/transfer", groupMemberController, "post:TransferOwner;options:Options"),
		web.NSRouter("/groups/:group_id/members", groupMemberController, "get:GetGroupMembers;post:AddGroupMember;options:Options"),
		web.NSRouter("/groups/:group_id/members/:member_id", groupMemberController, "delete:RemoveGroupMember;put:UpdateGroupMember;options:Options"),

		// 好友相关路由
		web.NSRouter("/friends", friendController, "get:GetFriendList;options:Options"),
		web.NSRouter("/friends/requests", friendController, "post:SendFriendRequest;get:GetFriendRequestList;options:Options"),
		web.NSRouter("/friends/requests/:request_id", friendController, "post:HandleFriendRequest;options:Options"),
		web.NSRouter("/friends/:friend_id", friendController, "delete:DeleteFriend;put:UpdateFriend;options:Options"),
		web.NSRouter("/users/search", friendController, "get:SearchUser;options:Options"),

		// 黑名单相关路由
		web.NSRouter("/blacklist", blacklistController, "post:AddToBlacklist;get:GetBlacklist;options:Options"),
		web.NSRouter("/blacklist/:blocked_id", blacklistController, "delete:RemoveFromBlacklist;options:Options"),
		web.NSRouter("/blacklist/check/:target_id", blacklistController, "get:IsBlocked;options:Options"),
	)

	// 注册命名空间
	web.AddNamespace(ns)
}
