/**
 * chat_dto.go
 * 聊天数据传输对象
 *
 * 该文件定义了聊天模块的数据传输对象，用于在不同层之间传递聊天相关数据
 */

package dto

import (
	"strconv"
	"time"
)

// MessageDTO 消息数据传输对象
type MessageDTO struct {
	ID         int64     `json:"id"`                  // 消息ID
	SessionID  int64     `json:"session_id"`          // 会话ID
	SenderID   int64     `json:"sender_id"`           // 发送者ID
	SenderType string    `json:"sender_type"`         // 发送者类型（user/merchant/system）
	Content    string    `json:"content"`             // 消息内容
	Type       string    `json:"type"`                // 消息类型（text/image/file/voice/notification）
	ResourceID string    `json:"resource_id"`         // 资源ID（图片、文件、语音等）
	FileName   string    `json:"file_name,omitempty"` // 文件名称
	FileSize   int64     `json:"file_size,omitempty"` // 文件大小（字节）
	FileType   string    `json:"file_type,omitempty"` // 文件MIME类型
	FileExt    string    `json:"file_ext,omitempty"`  // 文件扩展名
	Status     int       `json:"status"`              // 状态（0:未读, 1:已读）
	CreatedAt  time.Time `json:"created_at"`          // 创建时间

	// 通知消息相关字段
	NotificationType string      `json:"notification_type,omitempty"` // 通知类型
	NotificationData interface{} `json:"notification_data,omitempty"` // 通知数据（JSON对象，前端可用于业务跳转）
	Priority         int         `json:"priority,omitempty"`          // 优先级（1:低, 2:中, 3:高）
	Persistent       bool        `json:"persistent,omitempty"`        // 是否持久化
	ExpireAt         *time.Time  `json:"expire_at,omitempty"`         // 过期时间

	// 扩展字段（可能从用户或商家表中获取）
	SenderName   string `json:"sender_name,omitempty"`   // 发送者名称
	SenderAvatar string `json:"sender_avatar,omitempty"` // 发送者头像
}

// SessionDTO 会话数据传输对象
type SessionDTO struct {
	ID            int64     `json:"id"`                 // 会话ID
	Type          string    `json:"type"`               // 会话类型
	CreatorID     int64     `json:"creator_id"`         // 创建者ID
	CreatorType   string    `json:"creator_type"`       // 创建者类型
	ReceiverID    int64     `json:"receiver_id"`        // 接收者ID
	ReceiverType  string    `json:"receiver_type"`      // 接收者类型
	GroupID       int64     `json:"group_id,omitempty"` // 群ID（当会话类型为群聊时）
	LastMessageID int64     `json:"last_message_id"`    // 最后一条消息ID
	UnreadCount   int       `json:"unread_count"`       // 未读消息计数
	Status        int       `json:"status"`             // 状态（0:正常, 1:已关闭）
	CreatedAt     time.Time `json:"created_at"`         // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`         // 更新时间

	// 扩展字段
	LastMessage  *MessageDTO `json:"last_message,omitempty"`  // 最后一条消息
	TargetName   string      `json:"target_name,omitempty"`   // 对话目标名称
	TargetAvatar string      `json:"target_avatar,omitempty"` // 对话目标头像

	// 客户在线状态信息（仅当对话目标是客户时有效）
	CustomerInfo *CustomerOnlineInfo `json:"customer_info,omitempty"` // 客户在线状态信息
}

// CustomerOnlineInfo 客户在线状态信息
type CustomerOnlineInfo struct {
	ID           int64   `json:"id"`                  // 客户ID
	Name         string  `json:"name"`                // 客户名称
	Avatar       string  `json:"avatar"`              // 客户头像
	IsOnline     bool    `json:"is_online"`           // 是否在线
	OnlineStatus string  `json:"online_status"`       // 详细在线状态（active/idle/offline）
	LastSeen     *string `json:"last_seen,omitempty"` // 最后在线时间（ISO格式）
}

// WebSocketMessageDTO WebSocket消息传输对象
type WebSocketMessageDTO struct {
	Type      string      `json:"type"`       // 消息类型 (message, notification, heartbeat, business_notification)
	Event     string      `json:"event"`      // 事件类型 (new_message, message_read, session_update, order_paid, runner_accepted, etc.)
	SessionID int64       `json:"session_id"` // 会话ID
	Data      interface{} `json:"data"`       // 消息数据
	Timestamp interface{} `json:"timestamp"`  // 消息时间戳 (支持int64和string格式)
}

// GetTimestamp 获取标准化的时间戳（Unix时间戳）
func (w *WebSocketMessageDTO) GetTimestamp() int64 {
	switch t := w.Timestamp.(type) {
	case int64:
		return t
	case float64:
		return int64(t)
	case string:
		// 尝试解析ISO时间格式
		if parsedTime, err := time.Parse(time.RFC3339, t); err == nil {
			return parsedTime.Unix()
		}
		// 尝试解析Unix时间戳字符串
		if timestamp, err := strconv.ParseInt(t, 10, 64); err == nil {
			return timestamp
		}
		// 解析失败，返回当前时间
		return time.Now().Unix()
	default:
		// 未知格式，返回当前时间
		return time.Now().Unix()
	}
}

// 请求参数DTO

// CreateSessionRequest 创建会话请求
type CreateSessionRequest struct {
	ReceiverID   int64  `json:"receiver_id" valid:"Required"`   // 接收者ID
	ReceiverType string `json:"receiver_type" valid:"Required"` // 接收者类型
}

// SendTextMessageRequest 发送文本消息请求
type SendTextMessageRequest struct {
	Content string `json:"content" valid:"Required"` // 消息内容
}

// SendMediaMessageRequest 发送媒体消息请求
type SendMediaMessageRequest struct {
	Type string `json:"type" valid:"Required"` // 媒体类型 (image, file, voice)
	// 文件会通过multipart/form-data上传，不在此处定义
}

// SendMediaURLRequest 发送媒体URL消息请求
type SendMediaURLRequest struct {
	Content     string `json:"content" valid:"Required"`      // 媒体文件URL
	MessageType string `json:"message_type" valid:"Required"` // 媒体类型 (image, file, voice, video)
	FileName    string `json:"file_name,omitempty"`           // 文件名称
	FileSize    int64  `json:"file_size,omitempty"`           // 文件大小（字节）
	FileType    string `json:"file_type,omitempty"`           // 文件MIME类型
	FileExt     string `json:"file_ext,omitempty"`            // 文件扩展名
}

// SendMessageRequest 统一消息发送请求
type SendMessageRequest struct {
	SessionID int64  `json:"session_id" valid:"Required"` // 会话ID
	Type      string `json:"type" valid:"Required"`       // 消息类型 (text, image, file, voice)
	Content   string `json:"content"`                     // 文本内容 (当type=text时使用)
	// 媒体文件通过multipart/form-data上传，不在此处定义
}

// UpdateSessionRequest 更新会话请求
type UpdateSessionRequest struct {
	Status int `json:"status" valid:"Range(0|1)"` // 状态（0: 正常, 1: 已关闭）
}

// BusinessNotificationDTO 业务通知数据传输对象
type BusinessNotificationDTO struct {
	Type       string                 `json:"type"`        // 通知类型 (order_paid, runner_accepted, etc.)
	Title      string                 `json:"title"`       // 通知标题
	Content    string                 `json:"content"`     // 通知内容
	OrderID    int64                  `json:"order_id"`    // 关联订单ID
	OrderNo    string                 `json:"order_no"`    // 关联订单号
	Priority   int                    `json:"priority"`    // 优先级 (1-低 2-中 3-高)
	Persistent bool                   `json:"persistent"`  // 是否持久化存储
	ExpireTime int64                  `json:"expire_time"` // 过期时间戳
	Data       map[string]interface{} `json:"data"`        // 附加数据
	CreatedAt  int64                  `json:"created_at"`  // 创建时间戳
}

// OrderNotificationData 订单通知数据
type OrderNotificationData struct {
	OrderID      int64   `json:"order_id"`      // 订单ID
	OrderNo      string  `json:"order_no"`      // 订单号
	UserID       int64   `json:"user_id"`       // 用户ID
	MerchantID   int64   `json:"merchant_id"`   // 商家ID
	Status       int     `json:"status"`        // 订单状态
	PayAmount    float64 `json:"pay_amount"`    // 支付金额
	PayMethod    int     `json:"pay_method"`    // 支付方式
	PayTime      int64   `json:"pay_time"`      // 支付时间
	ProductName  string  `json:"product_name"`  // 商品名称
	ProductImage string  `json:"product_image"` // 商品图片
}

// RunnerNotificationData 跑腿通知数据
type RunnerNotificationData struct {
	RunnerOrderID int64   `json:"runner_order_id"` // 跑腿订单ID
	OrderNo       string  `json:"order_no"`        // 订单号
	UserID        int64   `json:"user_id"`         // 用户ID
	RunnerID      int64   `json:"runner_id"`       // 跑腿员ID
	RunnerName    string  `json:"runner_name"`     // 跑腿员姓名
	RunnerPhone   string  `json:"runner_phone"`    // 跑腿员电话
	RunnerAvatar  string  `json:"runner_avatar"`   // 跑腿员头像
	Status        int     `json:"status"`          // 跑腿订单状态
	DeliveryFee   float64 `json:"delivery_fee"`    // 配送费用
	PickupAddr    string  `json:"pickup_addr"`     // 取货地址
	DeliveryAddr  string  `json:"delivery_addr"`   // 配送地址
	EstimateTime  int64   `json:"estimate_time"`   // 预计送达时间
	AcceptTime    int64   `json:"accept_time"`     // 接单时间
	PickupTime    int64   `json:"pickup_time"`     // 取货时间
	DeliveryTime  int64   `json:"delivery_time"`   // 送达时间
}

// MerchantNotificationData 商家通知数据
type MerchantNotificationData struct {
	MerchantID   int64   `json:"merchant_id"`   // 商家ID
	MerchantName string  `json:"merchant_name"` // 商家名称
	OrderCount   int     `json:"order_count"`   // 订单数量
	TotalAmount  float64 `json:"total_amount"`  // 总金额
	Message      string  `json:"message"`       // 消息内容
}

// UserNotificationData 用户通知数据
type UserNotificationData struct {
	UserID     int64  `json:"user_id"`     // 用户ID
	UserName   string `json:"user_name"`   // 用户名称
	UserAvatar string `json:"user_avatar"` // 用户头像
	Message    string `json:"message"`     // 消息内容
	ActionType string `json:"action_type"` // 操作类型
	ActionURL  string `json:"action_url"`  // 操作链接
}

// SystemNotificationData 系统通知数据
type SystemNotificationData struct {
	Title      string `json:"title"`       // 通知标题
	Content    string `json:"content"`     // 通知内容
	Type       string `json:"type"`        // 通知类型 (maintenance, announcement, etc.)
	Level      string `json:"level"`       // 通知级别 (info, warning, error)
	StartTime  int64  `json:"start_time"`  // 开始时间
	EndTime    int64  `json:"end_time"`    // 结束时间
	ActionType string `json:"action_type"` // 操作类型
	ActionURL  string `json:"action_url"`  // 操作链接
}

// MessageCategoryDTO 消息分类数据传输对象
type MessageCategoryDTO struct {
	Type        string `json:"type"`        // 分类类型 (chat/system/order/service)
	Title       string `json:"title"`       // 分类标题
	Icon        string `json:"icon"`        // 分类图标
	Color       string `json:"color"`       // 分类颜色
	UnreadCount int    `json:"unreadCount"` // 未读消息数量
	Path        string `json:"path"`        // 跳转路径
}

// MessageCategoryListResponse 消息分类列表响应
type MessageCategoryListResponse struct {
	Categories []MessageCategoryDTO `json:"data"`
}

// UnreadCountDTO 未读消息统计数据传输对象
type UnreadCountDTO struct {
	Total         int            `json:"total"`         // 总未读数量
	Categories    map[string]int `json:"categories"`    // 各分类未读数量
	Conversations map[string]int `json:"conversations"` // 各会话未读数量
}

// MessageListRequest 消息列表请求参数
type MessageListRequest struct {
	Page     int    `json:"page" form:"page"`         // 页码
	PageSize int    `json:"pageSize" form:"pageSize"` // 每页数量
	Type     string `json:"type" form:"type"`         // 消息类型筛选
	Keyword  string `json:"keyword" form:"keyword"`   // 搜索关键词
}

// MessageListItem 消息列表项
type MessageListItem struct {
	ID          string `json:"id"`          // 消息ID
	Type        string `json:"type"`        // 消息类型
	Title       string `json:"title"`       // 消息标题
	Avatar      string `json:"avatar"`      // 头像
	LastMessage string `json:"lastMessage"` // 最后消息内容
	LastTime    int64  `json:"lastTime"`    // 最后消息时间
	UnreadCount int    `json:"unreadCount"` // 未读数量
	IsPinned    bool   `json:"isPinned"`    // 是否置顶
	IsMuted     bool   `json:"isMuted"`     // 是否静音
}

// MessageListResponse 消息列表响应
type MessageListResponse struct {
	List     []MessageListItem `json:"list"`     // 消息列表
	Total    int               `json:"total"`    // 总数量
	Page     int               `json:"page"`     // 当前页码
	PageSize int               `json:"pageSize"` // 每页数量
	HasMore  bool              `json:"hasMore"`  // 是否有更多
}

// UserTarget 用户目标对象（用于推送通知）
type UserTarget struct {
	UserID   int64  `json:"user_id"`   // 用户ID
	UserType string `json:"user_type"` // 用户类型
}

// PushNotificationRequest 推送通知请求
type PushNotificationRequest struct {
	Type             string                 `json:"type"`              // 通知类型
	Title            string                 `json:"title"`             // 通知标题
	Content          string                 `json:"content"`           // 通知内容
	NotificationData map[string]interface{} `json:"notification_data"` // 通知数据
	Priority         int                    `json:"priority"`          // 优先级（1:低，2:中，3:高）
	Persistent       bool                   `json:"persistent"`        // 是否持久化
	TargetUsers      []UserTarget           `json:"target_users"`      // 目标用户列表
	ExpireHours      int                    `json:"expire_hours"`      // 过期时间（小时）
}

// PushNotificationResponse 推送通知响应
type PushNotificationResponse struct {
	NotificationID int64 `json:"notification_id"` // 通知ID
	SuccessCount   int   `json:"success_count"`   // 成功推送数量
	FailureCount   int   `json:"failure_count"`   // 失败推送数量
	TotalCount     int   `json:"total_count"`     // 总推送数量
}
