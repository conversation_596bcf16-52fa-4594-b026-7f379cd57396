# WebSocket异步消息保存功能实现文档

## 问题描述

经过测试发现，WebSocket发送完消息后并没有将消息保存至数据库。需要修复这个bug，要求：
1. 接口调用WebSocket的服务应该是异步操作，不能阻塞原来API进程
2. 将消息保存至数据库的服务也应该是异步操作，不能阻塞原来API进程

## 解决方案

### 1. 异步消息服务架构

创建了 `AsyncMessageService` 异步消息服务，采用以下架构：

```
API接口 → WebSocket服务 → 异步消息服务 → 数据库
   ↓           ↓              ↓
立即返回    立即发送      队列处理 + 工作协程池
```

### 2. 核心组件

#### 2.1 异步消息服务 (`async_message_service.go`)

**主要功能：**
- 工作协程池管理（默认10个工作协程）
- 任务队列缓冲（默认队列大小1000）
- 批处理优化（可配置批处理大小和超时时间）
- 错误重试机制
- 优雅关闭机制

**核心接口：**
```go
type AsyncMessageService interface {
    Start() error
    Stop()
    SaveNotificationMessageAsync(ctx context.Context, task *AsyncMessageTask) error
    SaveTextMessageAsync(ctx context.Context, task *AsyncMessageTask) error
    SaveSystemMessageAsync(ctx context.Context, task *AsyncMessageTask) error
}
```

**任务结构：**
```go
type AsyncMessageTask struct {
    MessageType string                      // 消息类型：notification, text, system
    SenderID    int64                      // 发送者ID
    SenderType  string                     // 发送者类型：user, merchant, admin, system
    TargetID    int64                      // 接收者ID
    TargetType  string                     // 接收者类型：user, merchant, admin
    Content     string                     // 消息内容
    WSMessage   *dto.WebSocketMessageDTO   // WebSocket消息对象
    NotifyData  map[string]interface{}     // 通知数据
    CreatedAt   time.Time                  // 创建时间
    RetryCount  int                        // 重试次数
}
```

#### 2.2 WebSocket服务集成

**修改的服务文件：**
- `websocket_common.go` - 通用WebSocket服务
- `websocket_admin.go` - 管理员WebSocket服务  
- `websocket_merchant.go` - 商家WebSocket服务
- `websocket_user.go` - 用户WebSocket服务

**集成方式：**
每个WebSocket服务在发送消息后，都会创建异步任务来保存消息：

```go
// 发送WebSocket消息
err := s.wsManager.SendToAllUserDevices(targetID, targetType, wsMessage)
if err != nil {
    return err
}

// 异步保存消息到数据库
if s.asyncMessageService != nil {
    task := &AsyncMessageTask{
        MessageType: "notification",
        SenderID:    senderID,
        TargetID:    targetID,
        Content:     content,
        WSMessage:   wsMessage,
        NotifyData:  data,
    }
    
    // 异步保存，不阻塞当前流程
    go func() {
        if err := s.asyncMessageService.SaveNotificationMessageAsync(ctx, task); err != nil {
            logs.Error("异步保存消息失败: %v", err)
        }
    }()
}
```

#### 2.3 服务容器集成

**修改 `service_container.go`：**
- 添加 `AsyncMessageService` 字段
- 添加 getter/setter 方法
- 支持懒加载初始化

#### 2.4 路由初始化

**修改 `router.go`：**
- 在路由初始化时创建并启动异步消息服务
- 将服务实例保存到服务容器

### 3. 实现特点

#### 3.1 非阻塞设计
- WebSocket消息发送立即返回
- 数据库保存操作在后台异步执行
- API接口响应时间不受数据库操作影响

#### 3.2 高性能
- 工作协程池控制并发数量
- 任务队列缓冲突发流量
- 批处理优化减少数据库压力

#### 3.3 高可靠性
- 错误重试机制
- 优雅关闭确保消息不丢失
- 详细的错误日志记录

#### 3.4 易扩展
- 支持不同类型的消息保存
- 可配置的工作协程数量和队列大小
- 模块化设计便于维护

### 4. 消息类型支持

#### 4.1 通知消息 (notification)
- 订单状态通知
- 支付成功通知
- 退款通知
- 系统异常通知
- 业务状态通知

#### 4.2 文本消息 (text)
- 用户间聊天消息
- 客服消息
- 群组消息

#### 4.3 系统消息 (system)
- 系统维护通知
- 政策更新通知
- 安全提醒

### 5. 测试验证

创建了测试文件验证功能：
- `async_message_test.go` - 单元测试
- `demo_async_message.go` - 功能演示

**测试结果：**
- ✅ 异步消息服务创建成功
- ✅ 异步消息任务创建成功
- ✅ WebSocket服务集成成功
- ✅ 编译通过，无语法错误

### 6. 部署说明

#### 6.1 配置参数
可以通过配置文件调整以下参数：
- 工作协程数量（默认：10）
- 任务队列大小（默认：1000）
- 批处理大小（默认：50）
- 批处理超时时间（默认：5秒）
- 最大重试次数（默认：3）

#### 6.2 监控指标
建议监控以下指标：
- 队列长度
- 处理成功率
- 平均处理时间
- 错误重试次数

### 7. 总结

**已完成的工作：**
1. ✅ 创建异步消息服务 (`AsyncMessageService`)
2. ✅ 集成到所有WebSocket服务中
3. ✅ 更新服务容器和路由初始化
4. ✅ 实现非阻塞的消息保存机制
5. ✅ 添加错误处理和重试机制
6. ✅ 创建测试和演示代码
7. ✅ 编译验证通过

**解决的问题：**
- ✅ WebSocket消息发送后异步保存到数据库
- ✅ 不阻塞API接口响应
- ✅ 支持高并发和突发流量
- ✅ 提供可靠的消息持久化机制

**技术优势：**
- 🚀 高性能：异步处理，不阻塞主流程
- 🔒 高可靠：错误重试，优雅关闭
- 📈 可扩展：模块化设计，易于维护
- 🎯 易监控：详细日志，性能指标

现在WebSocket消息发送和数据库保存都是异步操作，完全不会阻塞原来的API进程！
