# 退款通知action_url错误修复报告

## 🔍 问题描述

用户反馈：商家对退款操作时，通知用户并保存消息结果时，构建的消息中 `action_url` 使用了错误的ID。

**问题详情**：
- 当前消息：`{type: "notification", event: "user_refund_result", session_id: 0,...}`
- 数据中：`action_url: "/user/takeout/order/17"`
- **错误**：URL中的17是 `refund_id`，但应该是 `order_id`（91）

## 🕵️ 问题分析

### 1. 根本原因
在用户退款通知中，`action_url` 错误地使用了 `refundID` 而不是 `orderID`：

```go
// 错误的实现
"action_url": fmt.Sprintf("/user/takeout/order/%d", refundID),
```

### 2. 影响范围
- **用户退款结果通知** (`SendRefundResultNotification`)
- **用户退款进度通知** (`SendRefundProgressNotification`)

### 3. 业务逻辑分析
- **用户视角**：用户需要查看的是订单详情，而不是退款详情
- **商家视角**：商家需要查看的是退款详情（商家的实现是正确的）
- **前端跳转**：用户点击通知应该跳转到订单页面查看退款状态

## 🛠️ 修复方案

### 1. 修复用户退款结果通知

**文件**：`modules/chat/services/websocket_user.go`

**修复前**：
```go
data := map[string]interface{}{
    // ... 其他字段 ...
    "action_url": fmt.Sprintf("/user/takeout/order/%d", refundID), // ❌ 错误
}
```

**修复后**：
```go
data := map[string]interface{}{
    // ... 其他字段 ...
    "action_url": fmt.Sprintf("/user/takeout/order/%d", orderID), // ✅ 正确
}
```

### 2. 修复用户退款进度通知

**同时修复了 `SendRefundProgressNotification` 方法**：

1. **增加缺失的参数**：添加了 `orderID` 和 `orderNo` 参数
2. **修复action_url**：使用 `orderID` 而不是 `refundID`
3. **更新接口定义**：同步更新了接口方法签名

**修复前**：
```go
func SendRefundProgressNotification(ctx context.Context, userID int64, refundID int64, refundNo string, progress string, estimatedTime string) error
```

**修复后**：
```go
func SendRefundProgressNotification(ctx context.Context, userID int64, refundID int64, refundNo string, orderID int64, orderNo string, progress string, estimatedTime string) error
```

## 📋 修复详情

### 1. 接口定义更新
```go
// WebSocketUserService 接口
SendRefundProgressNotification(ctx context.Context, userID int64, refundID int64, refundNo string, orderID int64, orderNo string, progress string, estimatedTime string) error
```

### 2. 实现方法修复
```go
// SendRefundResultNotification 修复
"action_url": fmt.Sprintf("/user/takeout/order/%d", orderID), // 使用orderID

// SendRefundProgressNotification 修复
"action_url": fmt.Sprintf("/user/takeout/order/%d", orderID), // 使用orderID
```

### 3. 数据完整性增强
退款进度通知现在包含完整的订单信息：
```go
data := map[string]interface{}{
    "refund_id":         refundID,
    "refund_no":         refundNo,
    "order_id":          orderID,    // ✅ 新增
    "order_no":          orderNo,    // ✅ 新增
    "progress":          progress,
    "estimated_time":    estimatedTime,
    // ... 其他字段 ...
}
```

## 🔄 修复对比

### 修复前的消息格式：
```json
{
  "type": "notification",
  "event": "user_refund_result",
  "data": {
    "refund_id": 17,
    "order_id": 91,
    "action_url": "/user/takeout/order/17", // ❌ 错误：使用refund_id
    // ... 其他字段 ...
  }
}
```

### 修复后的消息格式：
```json
{
  "type": "notification",
  "event": "user_refund_result",
  "data": {
    "refund_id": 17,
    "order_id": 91,
    "action_url": "/user/takeout/order/91", // ✅ 正确：使用order_id
    // ... 其他字段 ...
  }
}
```

## ✅ 验证结果

1. **编译测试**：项目编译成功，无语法错误 ✅
2. **接口一致性**：接口定义与实现保持一致 ✅
3. **数据完整性**：退款通知包含完整的订单和退款信息 ✅
4. **业务逻辑正确性**：用户点击通知跳转到正确的订单页面 ✅

## 🎯 影响范围

### 修复的通知类型：
- `user_refund_result` - 用户退款结果通知
- `user_refund_progress` - 用户退款进度通知

### 不受影响的通知类型：
- `merchant_refund_request` - 商家退款申请通知（使用refundID是正确的）
- `merchant_refund_status_update` - 商家退款状态更新通知（使用refundID是正确的）

## 📱 前端集成影响

修复后，前端可以正确处理用户退款通知的跳转：

```javascript
// 前端处理退款通知点击
function handleRefundNotificationClick(notificationData) {
    const { action_url, order_id } = notificationData;
    // 现在action_url正确指向订单详情页面
    router.push(action_url); // 跳转到 /user/takeout/order/91 而不是 /user/takeout/order/17
}
```

## 🎉 总结

此次修复解决了用户退款通知中 `action_url` 使用错误ID的问题：

1. **用户退款结果通知**：`action_url` 现在正确使用 `orderID`
2. **用户退款进度通知**：增强了参数完整性并修复了 `action_url`
3. **业务逻辑一致性**：用户通知跳转到订单页面，商家通知跳转到退款页面
4. **前端体验改善**：用户点击退款通知能正确跳转到对应的订单详情页面

修复后，用户收到退款通知时，点击通知将正确跳转到订单详情页面查看退款状态，而不是跳转到不存在的退款详情页面。
