# 支付方式显示错误修复验证

## 问题描述

在外卖订单创建API中选择余额支付并完成支付后，在高性能订单列表API中支付方式显示为"微信支付"而不是"余额支付"。

## 问题根源

支付方式常量映射不一致导致的显示错误：

### 修复前的错误映射
1. **订单仓库** (`modules/order/repositories/order_repository.go`)：
   ```go
   methodMap := map[int]string{
       0: "未选择",
       1: "微信支付", 
       2: "支付宝",
       3: "银行卡/余额支付",  // 错误：将余额支付和银行卡混合
   }
   ```

2. **订单服务** (`modules/order/services/order_service.go`)：
   ```go
   switch order.PayMethod {
   case 1: payMethodText = "支付宝"      // 错误：1应该是微信支付
   case 2: payMethodText = "微信支付"    // 错误：2应该是支付宝
   case 3: payMethodText = "银行卡"      // 错误：3应该是余额支付
   case 4: payMethodText = "余额支付"    // 错误：4应该是信用卡支付
   }
   ```

### 正确的常量定义
根据统一常量包 (`utils/constants/payment.go`)：
```go
const (
    PaymentMethodUnknown = 0      // 未知支付方式
    PaymentMethodWechat = 1       // 微信支付
    PaymentMethodAlipay = 2       // 支付宝
    PaymentMethodBalance = 3      // 余额支付
    PaymentMethodCreditCard = 4   // 信用卡支付
    PaymentMethodBankTransfer = 5 // 银行转账
    PaymentMethodCombination = 6  // 组合支付
)
```

## 修复内容

### 1. 修复订单仓库的支付方式映射
文件：`modules/order/repositories/order_repository.go`

**修复前**：
```go
func (r *OrderRepositoryImpl) getPayMethodText(payMethod int) string {
    methodMap := map[int]string{
        0: "未选择",
        1: "微信支付",
        2: "支付宝", 
        3: "银行卡/余额支付",
    }
    // ...
}
```

**修复后**：
```go
func (r *OrderRepositoryImpl) getPayMethodText(payMethod int) string {
    // 使用统一常量包的支付方式映射表
    if text, ok := constants.PaymentMethodMap[payMethod]; ok {
        return text
    }
    // 如果未找到，检查是否为未选择状态
    if payMethod == 0 {
        return "未选择"
    }
    return "未知支付方式"
}
```

### 2. 修复订单服务的支付方式映射
文件：`modules/order/services/order_service.go`

**修复前**：
```go
switch order.PayMethod {
case 1: payMethodText = "支付宝"      // 错误
case 2: payMethodText = "微信支付"    // 错误
case 3: payMethodText = "银行卡"      // 错误
case 4: payMethodText = "余额支付"    // 错误
}
```

**修复后**：
```go
switch order.PayMethod {
case 0: payMethodText = "未选择"
case 1: payMethodText = "微信支付"
case 2: payMethodText = "支付宝"
case 3: payMethodText = "余额支付"
case 4: payMethodText = "信用卡支付"
case 5: payMethodText = "银行转账"
case 6: payMethodText = "组合支付"
default: payMethodText = "未知支付方式"
}
```

## 验证步骤

### 1. 创建余额支付订单
```bash
curl -X POST "http://localhost:8080/api/v1/takeout/orders/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "takeoutAddressID": 1,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethodInt": 3,
    "clientIP": "*************",
    "deviceInfo": "Test Device",
    "merchantOrders": [{
      "merchantID": 1,
      "cartItemIDs": [1, 2],
      "deliveryTime": "2024-01-15 12:00:00"
    }]
  }'
```

### 2. 查询订单列表验证支付方式显示
```bash
curl -X GET "http://localhost:8080/api/v1/orders/simple/high-performance?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 预期结果
- 订单创建成功，支付方式为余额支付
- 订单列表中该订单的 `pay_method_text` 字段显示为 "余额支付"
- 不再显示为 "微信支付" 或 "银行卡/余额支付"

### 3. 修复外卖订单管理服务的支付方式映射
文件：`modules/takeout/services/admin_takeout_order_service.go`

**修复前**：
```go
func getPaymentMethodText(method int) string {
    switch method {
    case 1: return "微信支付"
    case 2: return "支付宝"
    case 3: return "银行卡"      // 错误：3应该是余额支付
    case 4: return "现金"        // 错误：4应该是信用卡支付
    case 5: return "余额支付"    // 错误：5应该是银行转账
    }
}
```

**修复后**：
```go
func getPaymentMethodText(method int) string {
    switch method {
    case 0: return "未选择"
    case 1: return "微信支付"
    case 2: return "支付宝"
    case 3: return "余额支付"
    case 4: return "信用卡支付"
    case 5: return "银行转账"
    case 6: return "组合支付"
    default: return "未知支付方式"
    }
}
```

### 4. 创建统一支付方式辅助工具
文件：`utils/payment_helper.go`

为了避免在多个地方重复定义支付方式映射，创建了统一的辅助工具函数：

```go
// GetPaymentMethodText 获取支付方式文本描述
func GetPaymentMethodText(payMethod int) string {
    if text, ok := constants.PaymentMethodMap[payMethod]; ok {
        return text
    }
    if payMethod == 0 {
        return "未选择"
    }
    return "未知支付方式"
}
```

## 影响范围

此修复影响以下API和服务的支付方式显示：
1. `GET /api/v1/orders/simple/high-performance` - 高性能订单列表
2. `GET /api/v1/orders/list` - 普通订单列表
3. 外卖订单管理相关API
4. 所有使用支付方式文本显示的地方

## 修复文件清单

1. `modules/order/repositories/order_repository.go` - 订单仓库支付方式映射
2. `modules/order/services/order_service.go` - 订单服务支付方式映射
3. `modules/takeout/services/admin_takeout_order_service.go` - 外卖管理服务支付方式映射
4. `utils/payment_helper.go` - 新增统一支付方式辅助工具

## 注意事项

1. **数据库数据不变**：此修复只影响显示逻辑，不改变数据库中存储的支付方式值
2. **向后兼容**：修复后的映射与统一常量包保持一致，确保系统一致性
3. **全面修复**：修复了所有发现的支付方式映射错误
4. **统一管理**：创建了统一的辅助工具，便于后续维护

## 测试建议

建议测试所有支付方式的显示：
- 未选择 (pay_method = 0) → "未选择"
- 微信支付 (pay_method = 1) → "微信支付"
- 支付宝 (pay_method = 2) → "支付宝"
- 余额支付 (pay_method = 3) → "余额支付"
- 信用卡支付 (pay_method = 4) → "信用卡支付"
- 银行转账 (pay_method = 5) → "银行转账"
- 组合支付 (pay_method = 6) → "组合支付"
